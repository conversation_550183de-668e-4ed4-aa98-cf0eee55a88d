'use client';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import { CustomAudioPlayer } from '@/common/components/CustomAudioPlayer';
import { Card, Grid, ActionIcon, Tooltip, Text, Group } from '@mantine/core';
import { IconLink, IconCheck } from '@tabler/icons-react';
import { ChatWindow } from './_components/ChatWindow';
import { SecondCol } from './_components/SecondCol';
import { SentimentChart } from './_components/SentimentChart';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
  Filler,
} from 'chart.js';

ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, ChartTooltip, Legend, Filler);

function escapeRegExp(str) {
  if (typeof str !== 'string') return '';
  return str.replace(/[-H\\^$*+?.()|[\]{}]/g, '\\$&');
}

const normalizeTurkish = (text) => {
  if (typeof text !== 'string') return '';
  return text
    .replace(/İ/g, 'i')
    .replace(/I/g, 'i')
    .replace(/ı/g, 'i')
    .replace(/Ö/g, 'o')
    .replace(/ö/g, 'o')
    .replace(/Ü/g, 'u')
    .replace(/ü/g, 'u')
    .replace(/Ş/g, 's')
    .replace(/ş/g, 's')
    .replace(/Ğ/g, 'g')
    .replace(/ğ/g, 'g')
    .replace(/Ç/g, 'c')
    .replace(/ç/g, 'c')
    .toLowerCase();
};

const formatTime = (timeInSeconds) => {
  const minutes = Math.floor(timeInSeconds / 60);
  const seconds = Math.floor(timeInSeconds % 60);
  return `${minutes}:${seconds < 10 ? `0${seconds}` : seconds}`;
};

export function ChannelDetailModal({
  channelType,
  id,
  fetchAuthClient,
  permissions,
  tenantParameters,
  agents,
  initSearchTerm = '',
}) {
  const [detail, setDetail] = useState(null);
  const [currentSentence, setCurrentSentence] = useState(null);
  const [searchTerm, setSearchTerm] = useState(initSearchTerm);
  const [currentTime, setCurrentTime] = useState(0);
  const [currentMessageModified, setCurrentMessageModified] = useState(null);
  const [analysisResultId, setAnalysisResultId] = useState(null);
  const [selectedLanguage, setSelectedLanguage] = useState(null);
  const [agentPercentage, setAgentPercentage] = useState(null);
  const [customerPercentage, setCustomerPercentage] = useState(null);
  const [copySuccess, setCopySuccess] = useState(false);
  const audioPlayerRef = useRef(null);
  console.log(initSearchTerm);
  //#region: Fetching and setting datas
  const fetchDetail = async () => {
    const response = await fetchAuthClient(channelType + '/detail/' + id);
    if (!response.ok) {
      return;
    }
    const data = await response.json();
    setDetail(data);
    setSelectedLanguage(data.dto.language);
    setAnalysisResultId(data.currentAnalysisResultId);

    if (data.dto.agentPercentage !== undefined && data.dto.customerPercentage !== undefined) {
      setAgentPercentage(data.dto.agentPercentage);
      setCustomerPercentage(data.dto.customerPercentage);
    }
  };

  const formatPercentage = (value) => {
    return value ? value.toFixed(2) + '%' : 'N/A';
  };

  useEffect(() => {
    fetchDetail();
  }, []);

  useEffect(() => {
    if (detail && channelType === 'Call') {
      for (const sentence of detail.sentences) {
        if (currentTime >= sentence.start && currentTime <= sentence.end && currentTime != 0) {
          setCurrentSentence(sentence);
          break;
        }
      }
    }
  }, [currentTime, detail, channelType]);

  const prepareSentimentChartData = useCallback(() => {
    if (!detail || !detail.sentences) return null;

    const sorted = [...detail.sentences].sort((a, b) => a.start - b.start);

    const startTime = sorted.length > 0 ? sorted[0].start : 0;
    const endTime = sorted.length > 0 ? Math.max(...sorted.map((s) => s.end)) : 0;

    const agentData = [];
    const customerData = [];

    sorted.forEach((s) => {
      if (s.isAgent) {
        agentData.push({
          x: s.start,
          y: s.sentiment ?? null,
          message: s.text,
          timeRange: `${formatTime(s.start)}-${formatTime(s.end)}`,
        });
      } else {
        customerData.push({
          x: s.start,
          y: s.sentiment ?? null,
          message: s.text,
          timeRange: `${formatTime(s.start)}-${formatTime(s.end)}`,
        });
      }
    });

    agentData.sort((a, b) => a.x - b.x);
    customerData.sort((a, b) => a.x - b.x);

    const filteredAgentData = agentData.filter((point) => point.y !== null);
    const filteredCustomerData = customerData.filter((point) => point.y !== null);

    return {
      startTime,
      endTime,
      agentData: filteredAgentData,
      customerData: filteredCustomerData,
    };
  }, [detail]);

  const copyChannelUrl = async () => {
    try {
      const url = `${window.location.origin}/v2.0/channelsDetail?channelType=${channelType}&id=${id}`;
      await navigator.clipboard.writeText(url);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (err) {
      console.error('Failed to copy URL:', err);
    }
  };

  if (!detail || !agents) {
    return <></>;
  }

  return (
    <>
      {channelType === 'Call' && (
        <Card shadow="sm" padding="lg" radius="md" withBorder mb="md">
          <Group justify="space-between" align="center" mb="sm">
            <Text size="sm" c="dimmed">
              Audio Player
            </Text>
            <Tooltip label={copySuccess ? 'Link kopyalandı!' : 'Kanal linkini kopyala'}>
              <ActionIcon variant="light" size="sm" onClick={copyChannelUrl} color={copySuccess ? 'green' : 'blue'}>
                {copySuccess ? <IconCheck size={14} /> : <IconLink size={14} />}
              </ActionIcon>
            </Tooltip>
          </Group>
          <CustomAudioPlayer
            type={detail.dto.type}
            sound={detail.sound}
            maxDuration={detail.dto.duration}
            ref={audioPlayerRef}
            onCurrentTimeChange={(curTimeStamp) => {
              setCurrentTime(curTimeStamp);
            }}
          />
        </Card>
      )}

      <Card shadow="sm" padding="lg" radius="md" withBorder>
        {channelType !== 'Call' && (
          <Group justify="flex-end" mb="sm">
            <Tooltip label={copySuccess ? 'Link kopyalandı!' : 'Kanal linkini kopyala'}>
              <ActionIcon variant="light" size="sm" onClick={copyChannelUrl} color={copySuccess ? 'green' : 'blue'}>
                {copySuccess ? <IconCheck size={14} /> : <IconLink size={14} />}
              </ActionIcon>
            </Tooltip>
          </Group>
        )}
        <Grid>
          <Grid.Col span={{ md: 4 }}>
            <ChatWindow
              channelType={channelType}
              detail={detail}
              permissions={permissions}
              id={id}
              selectedLanguage={selectedLanguage}
              setSelectedLanguage={setSelectedLanguage}
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
              currentSentence={currentSentence}
              currentTime={currentTime}
              currentMessageModified={currentMessageModified}
              setCurrentMessageModified={setCurrentMessageModified}
              analysisResultId={analysisResultId}
              audioPlayerRef={audioPlayerRef}
              fetchAuthClient={fetchAuthClient}
              setDetail={setDetail}
              normalizeTurkish={normalizeTurkish}
              escapeRegExp={escapeRegExp}
            />
          </Grid.Col>
          <Grid.Col span={{ md: 8 }}>
            <SecondCol
              detail={detail}
              channelType={channelType}
              agents={agents}
              tenantParameters={tenantParameters}
              setSearchTerm={setSearchTerm}
              id={id}
              analysisResultId={analysisResultId}
              fetchAuthClient={fetchAuthClient}
              permissions={permissions}
              agentPercentage={agentPercentage}
              customerPercentage={customerPercentage}
              formatPercentage={formatPercentage}
            />
          </Grid.Col>
        </Grid>
      </Card>

      {detail && detail.sentences && detail.sentences.length > 0 && (
        <SentimentChart formatTime={formatTime} prepareSentimentChartData={prepareSentimentChartData} />
      )}
    </>
  );
}

ChannelDetailModal.propTypes = {
  channelType: PropTypes.string.isRequired,
  id: PropTypes.string.isRequired,
  fetchAuthClient: PropTypes.func.isRequired,
  permissions: PropTypes.object.isRequired,
  tenantParameters: PropTypes.object.isRequired,
  agents: PropTypes.array.isRequired,
  initSearchTerm: PropTypes.string,
};
