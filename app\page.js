'use client';

import React, { useContext } from 'react';
import { AuthContext } from '@/common/contexts/AuthContext';

export default function Home() {
  const { user, permissions } = useContext(AuthContext);

  if (user && permissions) {
    if (permissions.includes('Call.View') && permissions.includes('Chat.View')) {
      window.location.href = '/' + process.env.VERSION;
      return null;
    } else if (permissions.includes('Call.View')) {
      window.location.href = '/' + process.env.VERSION + '/channels?channelType=Call';
      return null;
    } else if (permissions.includes('Chat.View')) {
      window.location.href = '/' + process.env.VERSION + '/channels?channelType=Chat';
      return null;
    }
  }

  return <></>;
}
