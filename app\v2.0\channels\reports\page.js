'use client';

import React, { useState, useEffect, useContext } from 'react';
import { AuthContext } from '@/common/contexts/AuthContext';
import { PluktoContext } from '@/common/contexts/PluktoContext';
import { useSearchParams } from 'next/navigation';
import {
  Container,
  Title,
  Card,
  Group,
  Button,
  Stack,
  Text,
  Badge,
  Grid,
  Paper,
  Divider,
  Alert,
  Loader,
  Center,
} from '@mantine/core';
import { notifications } from '@mantine/notifications';
import {
  IconFileAnalytics,
  IconDownload,
  IconSettings,
  IconInfoCircle,
} from '@tabler/icons-react';
import ExcelTemplateManager from './_components/ExcelTemplateManager';
import { ReportExportService } from './_components/ReportExportService';
import { generateDailyAgentAnalysisReport } from './_components/DailyAgentAnalysisTemplate';

export default function ReportsPage() {
  const { permissions, fetchAuthClient } = useContext(AuthContext);
  const { getTenantParameters, getAgents } = useContext(PluktoContext);
  const searchParams = useSearchParams();
  const channelType = searchParams.get('channelType');

  const [loading, setLoading] = useState(false);
  const [templateModalOpened, setTemplateModalOpened] = useState(false);
  const [reportService, setReportService] = useState(null);

  // Permission check
  if (!permissions.includes(channelType + '.View')) {
    window.location.href = '/401';
    return null;
  }

  let label = '';
  if (channelType === 'Call') {
    label = 'Çağrı';
  } else if (channelType === 'Chat') {
    label = 'Yazışma';
  }

  const tenantParameters = getTenantParameters(channelType);
  const agents = getAgents(channelType);

  // ReportService'i initialize et
  useEffect(() => {
    if (agents && tenantParameters) {
      const service = new ReportExportService(fetchAuthClient, channelType, agents, tenantParameters);
      setReportService(service);
    }
  }, [fetchAuthClient, channelType, agents, tenantParameters]);

  // Template export handler
  const handleTemplateExport = async (exportConfig) => {
    if (!reportService) return;

    setLoading(true);
    try {
      if (exportConfig.type === 'template') {
        await reportService.exportTemplate(exportConfig);
      } else if (exportConfig.type === 'custom') {
        await reportService.exportCustom(exportConfig);
      }

      notifications.show({
        title: 'Başarılı',
        message: 'Rapor başarıyla oluşturuldu ve indirildi.',
        color: 'green',
      });

      setTemplateModalOpened(false);
    } catch (error) {
      console.error('Export error:', error);
      notifications.show({
        title: 'Hata',
        message: error.message || 'Rapor oluşturulurken bir hata oluştu.',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  // Günlük temsilci analizi direkt export
  const handleDailyAgentAnalysisExport = async () => {
    if (!reportService) return;

    setLoading(true);
    try {
      const dataToExport = await reportService.fetchFilteredData([], 5000);
      await generateDailyAgentAnalysisReport(
        dataToExport,
        agents,
        channelType,
        channelType === 'Call' ? 'Çağrı' : 'Yazışma'
      );

      notifications.show({
        title: 'Başarılı',
        message: 'Günlük temsilci analizi raporu başarıyla oluşturuldu.',
        color: 'green',
      });
    } catch (error) {
      console.error('Daily agent analysis export error:', error);
      notifications.show({
        title: 'Hata',
        message: error.message || 'Rapor oluşturulurken bir hata oluştu.',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  if (!tenantParameters || !agents) {
    return (
      <Center h={200}>
        <Loader size="lg" />
      </Center>
    );
  }

  return (
    <Container size="xl">
      <Group justify="space-between" mb="xl">
        <div>
          <Title order={2} mb="xs">
            {label} Raporları
          </Title>
          <Text c="dimmed" size="sm">
            Farklı formatlarda excel raporları oluşturun ve indirin
          </Text>
        </div>
        <Badge size="lg" variant="light" color="blue">
          {channelType === 'Call' ? 'Çağrılar' : 'Yazışmalar'}
        </Badge>
      </Group>

      <Alert icon={<IconInfoCircle size={16} />} mb="xl" color="blue" variant="light">
        <Text size="sm">
          Bu sayfada mevcut verilerinizi farklı formatlarda excel dosyası olarak export edebilirsiniz.
          Hem önceden tanımlanmış şablonları kullanabilir, hem de kendi özel raporunuzu oluşturabilirsiniz.
        </Text>
      </Alert>

      <Grid>
        {/* Sabit Template'ler */}
        <Grid.Col span={{ base: 12, md: 6 }}>
          <Card shadow="sm" padding="lg" radius="md" withBorder h="100%">
            <Group justify="space-between" mb="md">
              <Title order={4}>Hazır Şablonlar</Title>
              <IconFileAnalytics size={24} color="var(--mantine-color-blue-6)" />
            </Group>
            
            <Text size="sm" c="dimmed" mb="lg">
              Önceden hazırlanmış rapor şablonlarını kullanarak hızlıca rapor alın.
            </Text>

            <Stack gap="md">
              <Paper p="md" withBorder radius="sm" bg="blue.0">
                <Group justify="space-between" align="flex-start">
                  <div style={{ flex: 1 }}>
                    <Text fw={500} size="sm" mb="xs">
                      Günlük Temsilci Bazlı Analiz Raporu
                    </Text>
                    <Text size="xs" c="dimmed" mb="sm">
                      Temsilci bazında günlük analiz adedi ve kalite puanı tablosu
                    </Text>
                    <Group gap="xs">
                      <Badge size="xs" variant="light">Temsilci</Badge>
                      <Badge size="xs" variant="light">Kanal</Badge>
                      <Badge size="xs" variant="light">Firma</Badge>
                      <Badge size="xs" variant="light">AI Analiz</Badge>
                    </Group>
                  </div>
                  <Button
                    size="xs"
                    variant="light"
                    leftSection={<IconDownload size={14} />}
                    loading={loading}
                    onClick={handleDailyAgentAnalysisExport}
                  >
                    İndir
                  </Button>
                </Group>
              </Paper>

              <Paper p="md" withBorder radius="sm" bg="gray.0">
                <Group justify="space-between" align="center">
                  <div>
                    <Text fw={500} size="sm" c="dimmed">
                      Daha fazla şablon yakında...
                    </Text>
                    <Text size="xs" c="dimmed">
                      Yeni rapor şablonları eklenmektedir
                    </Text>
                  </div>
                  <Button size="xs" variant="subtle" disabled>
                    Yakında
                  </Button>
                </Group>
              </Paper>
            </Stack>
          </Card>
        </Grid.Col>

        {/* Custom Export */}
        <Grid.Col span={{ base: 12, md: 6 }}>
          <Card shadow="sm" padding="lg" radius="md" withBorder h="100%">
            <Group justify="space-between" mb="md">
              <Title order={4}>Özel Rapor Oluştur</Title>
              <IconSettings size={24} color="var(--mantine-color-teal-6)" />
            </Group>
            
            <Text size="sm" c="dimmed" mb="lg">
              Hangi verilerin dahil edileceğini seçerek kendi raporunuzu oluşturun.
            </Text>

            <Stack gap="md">
              <Paper p="md" withBorder radius="sm" bg="teal.0">
                <Text fw={500} size="sm" mb="xs">
                  Mevcut Veri Kaynakları
                </Text>
                <Stack gap="xs">
                  <Group gap="xs">
                    <Badge size="xs" color="blue">Kalite Analizi</Badge>
                    <Text size="xs" c="dimmed">Ana veri tablosu</Text>
                  </Group>
                  <Group gap="xs">
                    <Badge size="xs" color="green">Temsilci İstatistikleri</Badge>
                    <Text size="xs" c="dimmed">Agent performans verileri</Text>
                  </Group>
                  <Group gap="xs">
                    <Badge size="xs" color="orange">Kategori İstatistikleri</Badge>
                    <Text size="xs" c="dimmed">Kategori bazlı analizler</Text>
                  </Group>
                  <Group gap="xs">
                    <Badge size="xs" color="purple">Kalite Kuralı İstatistikleri</Badge>
                    <Text size="xs" c="dimmed">Kural bazlı performans</Text>
                  </Group>
                  <Group gap="xs">
                    <Badge size="xs" color="red">Haftalık Çağrı Analizi</Badge>
                    <Text size="xs" c="dimmed">Zaman bazlı analizler</Text>
                  </Group>
                </Stack>
              </Paper>

              <Button
                fullWidth
                variant="light"
                color="teal"
                leftSection={<IconSettings size={16} />}
                onClick={() => setTemplateModalOpened(true)}
              >
                Özel Rapor Oluştur
              </Button>
            </Stack>
          </Card>
        </Grid.Col>
      </Grid>

      <Divider my="xl" />

      <Paper p="lg" withBorder radius="md" bg="gray.0">
        <Group justify="center">
          <Text size="sm" c="dimmed" ta="center">
            Raporlar mevcut filtrelerinize göre oluşturulacaktır. 
            Detaylı filtreleme için ana kayıtlar sayfasını kullanabilirsiniz.
          </Text>
        </Group>
      </Paper>

      {/* Excel Template Manager Modal */}
      <ExcelTemplateManager
        channelType={channelType}
        tenantParameters={tenantParameters}
        agents={agents}
        onTemplateExport={handleTemplateExport}
        opened={templateModalOpened}
        onClose={() => setTemplateModalOpened(false)}
      />
    </Container>
  );
}
