export function secondsToText(seconds) {
  const dakika = Math.floor(seconds / 60);
  const saniye = seconds % 60;
  let str = '';
  if (parseInt(dakika) > 0) {
    str += `${parseInt(dakika)} dk `;
  }
  str += `${parseInt(saniye)} sn`;
  return str;
}

export function getDatesFromType(type) {
  const now = new Date();
  if (type === 'today') {
    const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0, 0);
    const endOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59, 999);
    return [startOfDay, endOfDay];
  } else if (type === 'lastoneday') {
    const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1, 0, 0, 0, 0);
    const endOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59, 999);
    return [startOfDay, endOfDay];
  } else if (type === 'lastoneweek') {
    const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 7, 0, 0, 0, 0);
    const endOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59, 999);
    return [startOfDay, endOfDay];
  } else if (type === 'lastonemonth') {
    const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 29, 0, 0, 0, 0);
    const endOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59, 999);
    return [startOfDay, endOfDay];
  } else if (type === 'lastsixmonth') {
    const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0, 0);
    startOfDay.setMonth(startOfDay.getMonth() - 6);
    const endOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59, 999);
    return [startOfDay, endOfDay];
  } else if (type === 'lastoneyear') {
    const startOfDay = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate(), 0, 0, 0, 0);
    const endOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59, 999);
    return [startOfDay, endOfDay];
  } else if (type === 'thisweek') {
    const day = now.getDay();
    const diffToMonday = (day + 6) % 7;
    const startOfWeek = new Date(now.getFullYear(), now.getMonth(), now.getDate() - diffToMonday, 0, 0, 0, 0);
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);
    endOfWeek.setHours(23, 59, 59, 999);
    return [startOfWeek, endOfWeek];
  } else if (type === 'thismonth') {
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0, 0);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);
    return [startOfMonth, endOfMonth];

  } else {
    return [null, null];
  }
}
