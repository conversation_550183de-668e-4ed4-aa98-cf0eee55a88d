import * as ExcelJS from 'exceljs';
import { generateDailyAgentAnalysisReport } from './DailyAgentAnalysisTemplate';

/**
 * Rapor export servisi - farklı template'ler ve custom export'lar için
 */
export class ReportExportService {
  constructor(fetchAuthClient, channelType, agents, tenantParameters) {
    this.fetchAuthClient = fetchAuthClient;
    this.channelType = channelType;
    this.agents = agents;
    this.tenantParameters = tenantParameters;
  }

  /**
   * Mevcut filtrelerle veri çekme
   */
  async fetchFilteredData(filters = [], pageSize = 5000) {
    try {
      const response = await this.fetchAuthClient(this.channelType + '/paged', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          page: 1,
          pageSize: pageSize,
          sortFilters: [],
          columnFilters: filters,
        }),
      });
      
      if (!response.ok) {
        throw new Error('Veri çekme hatası');
      }
      
      const json = await response.json();
      return json.items || [];
    } catch (error) {
      console.error('Veri çekme hatası:', error);
      throw error;
    }
  }

  /**
   * Template bazlı export
   */
  async exportTemplate(templateConfig, filters = []) {
    const pageSize = templateConfig.pageSize || 5000;
    const dataToExport = await this.fetchFilteredData(filters, pageSize);
    
    if (dataToExport.length === 0) {
      throw new Error('Export edilecek veri bulunamadı. Lütfen filtrelerinizi kontrol edin.');
    }

    switch (templateConfig.template.id) {
      case 'daily_agent_analysis':
        return await generateDailyAgentAnalysisReport(
          dataToExport,
          this.agents,
          this.channelType,
          this.channelType === 'Call' ? 'Çağrı' : 'Yazışma'
        );
      
      default:
        throw new Error('Bilinmeyen template: ' + templateConfig.template.id);
    }
  }

  /**
   * Custom export - kullanıcının seçtiği sheet'ler ve column'lar
   */
  async exportCustom(customConfig, filters = []) {
    const pageSize = customConfig.pageSize || 5000;
    const dataToExport = await this.fetchFilteredData(filters, pageSize);
    
    if (dataToExport.length === 0) {
      throw new Error('Export edilecek veri bulunamadı. Lütfen filtrelerinizi kontrol edin.');
    }

    const workbook = new ExcelJS.Workbook();
    const { sheets, columns } = customConfig;

    // Her seçili sheet için ayrı worksheet oluştur
    for (const sheetId of sheets) {
      const selectedColumns = columns[sheetId] || [];
      if (selectedColumns.length === 0) continue;

      switch (sheetId) {
        case 'quality_analysis':
          await this.createQualityAnalysisSheet(workbook, dataToExport, selectedColumns);
          break;
        case 'agent_stats':
          await this.createAgentStatsSheet(workbook, dataToExport, selectedColumns);
          break;
        case 'category_stats':
          await this.createCategoryStatsSheet(workbook, dataToExport, selectedColumns);
          break;
        case 'quality_rules_stats':
          await this.createQualityRulesStatsSheet(workbook, dataToExport, selectedColumns);
          break;
        case 'weekly_analysis':
          await this.createWeeklyAnalysisSheet(workbook, dataToExport, selectedColumns);
          break;
      }
    }

    // Excel dosyasını indir
    const buffer = await workbook.xlsx.writeBuffer();
    const blob = new Blob([buffer], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    
    const today = new Date().toLocaleDateString('tr-TR').replace(/\//g, '_');
    const channelText = this.channelType === 'Call' ? 'Çağrı' : 'Yazışma';
    link.download = `${channelText}_Özel_Rapor_${today}.xlsx`;
    link.click();
    window.URL.revokeObjectURL(url);

    return true;
  }

  /**
   * Kalite Analizi sheet'i oluştur
   */
  async createQualityAnalysisSheet(workbook, dataToExport, selectedColumns) {
    const worksheet = workbook.addWorksheet('Kalite Analizi');
    
    // Header'ları oluştur
    const headers = [];
    const columnMapping = {
      channel: 'Kanal',
      date: 'Tarih',
      time: 'Saat',
      identifier: 'Kimlik',
      agent: 'Temsilci',
      duration: 'Süre (sn)',
      point: 'Puan',
      vendor: 'Firma',
      category: 'Kategori',
      quality_criteria: 'Kalite Kriterleri'
    };

    selectedColumns.forEach(colId => {
      if (columnMapping[colId]) {
        headers.push(columnMapping[colId]);
      }
    });

    // Header row
    worksheet.addRow(headers);
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true, color: { argb: 'FFFFFF' } };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '4472C4' },
    };
    headerRow.alignment = { horizontal: 'center', vertical: 'middle' };

    // Veri satırları
    dataToExport.forEach((item) => {
      const row = [];
      selectedColumns.forEach(colId => {
        switch (colId) {
          case 'channel':
            row.push(this.channelType === 'Call' ? 'INBOUND' : 'CHAT');
            break;
          case 'date':
            row.push(item.date ? new Date(item.date).toLocaleDateString('tr-TR') : '');
            break;
          case 'time':
            row.push(item.date ? new Date(item.date).toLocaleTimeString('tr-TR') : '');
            break;
          case 'identifier':
            row.push(item.identifier || item.id || '');
            break;
          case 'agent':
            const agent = this.agents.find(a => a.id === item.agentId);
            row.push(agent ? `${agent.name} ${agent.surname}` : '');
            break;
          case 'duration':
            row.push(item.duration || 0);
            break;
          case 'point':
            row.push(item.info?.point || 0);
            break;
          case 'vendor':
            row.push(item.vendor || item.company || '');
            break;
          case 'category':
            row.push(item.info?.category || '');
            break;
          case 'quality_criteria':
            if (item.info?.analysis) {
              const failed = Object.entries(item.info.analysis)
                .filter(([_, v]) => v.isAgentHaveWrongAction === true)
                .map(([k]) => k);
              row.push(failed.join(', '));
            } else {
              row.push('');
            }
            break;
          default:
            row.push('');
        }
      });
      worksheet.addRow(row);
    });

    // Column genişlikleri
    worksheet.columns.forEach((column, index) => {
      column.width = 20;
    });
  }

  /**
   * Temsilci İstatistikleri sheet'i oluştur
   */
  async createAgentStatsSheet(workbook, dataToExport, selectedColumns) {
    const worksheet = workbook.addWorksheet('Temsilci İstatistikleri');
    
    // Temsilci bazlı istatistikleri hesapla
    const agentStats = {};
    dataToExport.forEach((item) => {
      const agentId = item.agentId;
      const agent = this.agents.find(a => a.id === agentId);
      const agentName = agent ? `${agent.name} ${agent.surname}` : `Agent ${agentId}`;
      const company = item.vendor || item.company || 'Bilinmeyen Şirket';

      if (!agentStats[agentId]) {
        agentStats[agentId] = {
          agentName,
          company,
          callCount: 0,
          totalDuration: 0,
          totalPoint: 0,
          validDurationCount: 0,
          validPointCount: 0,
        };
      }

      agentStats[agentId].callCount++;
      
      if (item.duration && !isNaN(item.duration)) {
        agentStats[agentId].totalDuration += Number(item.duration);
        agentStats[agentId].validDurationCount++;
      }
      
      if (item.info?.point && !isNaN(item.info.point)) {
        agentStats[agentId].totalPoint += Number(item.info.point);
        agentStats[agentId].validPointCount++;
      }
    });

    // Header'ları oluştur
    const headers = [];
    const columnMapping = {
      agent_name: 'Temsilci Adı',
      company: 'Firma',
      call_count: 'Çağrı Adedi',
      avg_duration: 'Ortalama Süre (sn)',
      avg_point: 'Ortalama Puan',
      avg_silence: 'Ortalama Sessizlik (sn)',
      quality_success_rates: 'Kalite Başarı Oranları'
    };

    selectedColumns.forEach(colId => {
      if (columnMapping[colId]) {
        headers.push(columnMapping[colId]);
      }
    });

    worksheet.addRow(headers);
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true, color: { argb: 'FFFFFF' } };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '4472C4' },
    };

    // Veri satırları
    Object.values(agentStats).forEach((stats) => {
      const row = [];
      selectedColumns.forEach(colId => {
        switch (colId) {
          case 'agent_name':
            row.push(stats.agentName);
            break;
          case 'company':
            row.push(stats.company);
            break;
          case 'call_count':
            row.push(stats.callCount);
            break;
          case 'avg_duration':
            row.push(stats.validDurationCount > 0 ? 
              (stats.totalDuration / stats.validDurationCount).toFixed(2) : '0');
            break;
          case 'avg_point':
            row.push(stats.validPointCount > 0 ? 
              (stats.totalPoint / stats.validPointCount).toFixed(2) : '0');
            break;
          case 'avg_silence':
            row.push('0'); // Placeholder
            break;
          case 'quality_success_rates':
            row.push('N/A'); // Placeholder
            break;
          default:
            row.push('');
        }
      });
      worksheet.addRow(row);
    });

    worksheet.columns.forEach((column) => {
      column.width = 25;
    });
  }

  // Diğer sheet oluşturma metodları placeholder olarak
  async createCategoryStatsSheet(workbook, dataToExport, selectedColumns) {
    const worksheet = workbook.addWorksheet('Kategori İstatistikleri');
    worksheet.addRow(['Kategori İstatistikleri - Yakında']);
  }

  async createQualityRulesStatsSheet(workbook, dataToExport, selectedColumns) {
    const worksheet = workbook.addWorksheet('Kalite Kuralı İstatistikleri');
    worksheet.addRow(['Kalite Kuralı İstatistikleri - Yakında']);
  }

  async createWeeklyAnalysisSheet(workbook, dataToExport, selectedColumns) {
    const worksheet = workbook.addWorksheet('Haftalık Çağrı Analizi');
    worksheet.addRow(['Haftalık Çağrı Analizi - Yakında']);
  }
}
