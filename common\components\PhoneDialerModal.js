import React, { useImperativeHandle, forwardRef, useState, useContext, useEffect } from 'react';
import { Modal, Button, TextInput, Select } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { handleFetchSubmit } from '@/common/functions/formFunctions';
import { AuthContext } from '@/common/contexts/AuthContext';
import { PluktoContext } from '@/common/contexts/PluktoContext';

const PhoneDialerModal = forwardRef((props, ref) => {
  const { fetchAuthClient, user, permissions } = useContext(AuthContext);
  const { callTenantParameters, callAgents } = useContext(PluktoContext);
  const [opened, { open, close }] = useDisclosure(false);
  const [dialedNumber, setDialedNumber] = useState('905');
  const [selectedAgentId, setSelectedAgentId] = useState(null);
  const [agentData, setAgentData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [realAgents, setRealAgents] = useState([]);
  const [selectedRealAgentId, setSelectedRealAgentId] = useState(null);
  useImperativeHandle(ref, () => ({
    openDialer: () => open(),
  }));

  useEffect(() => {
    const fetchAgentData = async () => {
      setIsLoading(true);
      try {
        if (callTenantParameters) {
          setAgentData(
            callTenantParameters.agents
              .filter(agent => {
                if(!agent.allowedUserIds)return false;
                if (agent.allowedUserIds.includes("All")) return true;
                if (permissions.includes("Call.DemoCallingViewAll")) return true;
                return  agent.allowedUserIds.includes(""+user.id);
              })
              .map(agent => ({
                value: "" + agent.id,
                label: agent.name
              }))
          );
        }
        
        if (callAgents) {
          setRealAgents(callAgents.map(agent => ({
            value: "" + agent.id,
            label: agent.name + " " + agent.surname
          })));
        }
      } catch (error) {
        console.error('Agent data fetch error:', error);
      } finally {
        setIsLoading(false);
      }
    };

    if (opened) {
      fetchAgentData();
    }
  }, [opened, callTenantParameters, callAgents, permissions, user.id]);

  const handleDigitClick = (digit) => {
    setDialedNumber((prev) => prev + digit);
  };

  const handleDelete = () => {
    setDialedNumber((prev) => prev.slice(0, -1));
  };

  const handleCall = async () => {
    if (!dialedNumber) return;
    try {
      handleFetchSubmit(
        fetchAuthClient('Call/demoCall/' + dialedNumber + '/' + selectedAgentId + "/" + selectedRealAgentId, {
          method: 'GET',
        }),
        (response) => {
          close();
          setDialedNumber('');
        }
      );
    } catch (error) {
      console.error('API Hatası:', error);
    }
  };

  const handleCloseModal = () => {
    close();
    setDialedNumber('');
  };

  return (
    <Modal opened={opened} onClose={handleCloseModal} withCloseButton={false}>
      <div>
      <Select
          placeholder="Konuşmanın Atanacağı Agent'ı Seçiniz"
          mb={'md'}
          value={selectedRealAgentId}
          onChange={setSelectedRealAgentId}
          data={realAgents}
          loading={isLoading}
        />
        <Select
          placeholder="Konuşacağınız Voice AI'ı Seçiniz"
          mb={'md'}
          value={selectedAgentId}
          onChange={setSelectedAgentId}
          data={agentData}
          loading={isLoading}
        />
        <TextInput value={dialedNumber} mb={'md'} onChange={(event) => setDialedNumber(event.currentTarget.value)} />
        <div
          style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(3, 1fr)',
            gap: '10px',
            marginBottom: '10px',
          }}
        >
          {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((num) => (
            <Button key={num} onClick={() => handleDigitClick(num.toString())}>
              {num}
            </Button>
          ))}
          <Button onClick={() => handleDigitClick('0')} style={{ gridColumn: '2 / 3' }}>
            0
          </Button>
        </div>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <Button color="red" onClick={handleDelete} fullWidth style={{ marginRight: '8px' }}>
            Sil
          </Button>
          <Button onClick={handleCall} color="violet" fullWidth style={{ marginLeft: '8px' }}>
            Ara
          </Button>
        </div>
      </div>
    </Modal>
  );
});

export default PhoneDialerModal;
