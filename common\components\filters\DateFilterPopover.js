'use client';
import React, { useState } from 'react';
import { ActionIcon, Button, Popover, Select, Stack } from '@mantine/core';
import { DatePicker } from '@mantine/dates';
import { IconCalendar, IconX } from '@tabler/icons-react';
import { getDatesFromType } from '@/common/functions/commonFunctions';

const DateFilterPopover = function ({ label, value, onChange, initDateTypeValue }) {
  const [opened, setOpened] = useState(false);
  const [tempValue, setTempValue] = useState(
    initDateTypeValue ? getDatesFromType(initDateTypeValue) : value ? value : [null, null]
  );
  const [dateTypeValue, setDateTypeValue] = useState(initDateTypeValue);

  const isEmptyValue = (val) => {
    return Array.isArray(val) && val.length === 2 && val[0] === null && val[1] === null;
  };

  return (
    <div style={{ display: 'flex', alignItems: 'center' }}>
      <Popover opened={opened} position="bottom" withArrow>
        <Popover.Target>
          <div>
            <Button
              leftSection={<IconCalendar />}
              size="xs"
              variant="light"
              radius="xl"
              color={isEmptyValue(value) ? 'black' : 'teal'}
              onClick={() => {
                if (opened) {
                  setOpened(false);
                } else {
                  setOpened(true);
                  setTempValue(value);
                }
              }}
            >
              {isEmptyValue(value)
                ? label
                : `${value[0] instanceof Date ? value[0].toLocaleString() : ''} - ${value[1] instanceof Date ? value[1].toLocaleString() : ''
                }`}
            </Button>
          </div>
        </Popover.Target>
        <Popover.Dropdown>
          <div style={{ display: 'flex', width: '100%' }}>
            <ActionIcon
              style={{ marginLeft: 'auto' }}
              variant="subtle"
              color="black"
              onClick={() => {
                setOpened(false);
                setTempValue(value);
              }}
            >
              <IconX />
            </ActionIcon>
          </div>
          <Stack spacing="sm">
            <Select
              label="Hazır Tarihler"
              value={dateTypeValue}
              onChange={(val) => {
                const date = getDatesFromType(val);
                setTempValue(date);
                setDateTypeValue(val);
              }}
              data={[
                { value: 'manuel', label: 'Manuel' },
                { value: 'today', label: 'Bugün' },
                { value: 'lastoneday', label: 'Son 1 Gün' },
                { value: 'thisweek', label: 'Bu Hafta' },
                { value: 'lastoneweek', label: 'Son 1 Hafta' },
                { value: 'thismonth', label: 'Bu Ay' },
                { value: 'lastonemonth', label: 'Son 1 Ay' },
                { value: 'lastsixmonth', label: 'Son 6 Ay' },
                { value: 'lastoneyear', label: 'Son 1 Yıl' },
              ]}
            />
            {dateTypeValue === 'manuel' && (
              <DatePicker
                type="range"
                value={tempValue}
                onChange={(val) => {
                  if (val[0] !== null && val[1] !== null) {
                    const start = new Date(val[0]);
                    start.setHours(0, 0, 0, 0);
                    const end = new Date(val[1]);
                    end.setHours(23, 59, 59, 999);
                    setTempValue([start, end]);
                  } else if (val[0] != null) {
                    const start = new Date(val[0]);
                    start.setHours(0, 0, 0, 0);
                    setTempValue([start, null]);
                  } else {
                    setTempValue([null, null]);
                  }
                }}
              />
            )}
            <Button
              onClick={() => {
                onChange(tempValue, isEmptyValue(tempValue));
                setOpened(false);
              }}
              size="xs"
              mt="sm"
            >
              Filtrele
            </Button>
          </Stack>
        </Popover.Dropdown>
      </Popover>
      {isEmptyValue(value) === false && (
        <ActionIcon
          variant="subtle"
          onClick={() => {
            setDateTypeValue('manuel');
            setTempValue([null, null]);
            onChange([null, null], true);
            setOpened(false);
          }}
        >
          <IconX />
        </ActionIcon>
      )}
    </div>
  );
};

export default DateFilterPopover;
