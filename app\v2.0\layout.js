'use client';
import React from 'react';
import {
  ActionIcon,
  Avatar,
  Code,
  Drawer,
  Group,
  Image,
  Menu,
  NavLink,
  Pill,
  ScrollArea,
  Select,
  Text,
  Tooltip,
  UnstyledButton,
} from '@mantine/core';
import classes from './NavbarNested.module.css';
import classes2 from './UserButton.module.css';
import {
  IconChevronRight,
  IconChevronLeft,
  IconHome,
  IconMessage,
  IconPhone,
  IconSearch,
  IconDatabase,
  IconDeviceMobile,
  IconRuler,
  IconUserBolt,
  IconShield,
  IconUsers,
  IconTrendingUp,
  IconFileAnalytics,
} from '@tabler/icons-react';
import { AuthContext } from '@/common/contexts/AuthContext';
import { useContext, useRef, useState } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { useDisclosure } from '@mantine/hooks';
import ProfileForm from '@/common/forms/ProfileForm';
import PhoneDialerModal from '@/common/components/PhoneDialerModal';
import { spotlight } from '@mantine/spotlight';
import { PluktoSearch } from '@/common/components/PluktoSearch';

export default function Layout({ children }) {
  const [isMenuShow, setMenuShow] = useState(true);
  const { user, permissions, tenant, tenants, fetchAuthClient } = useContext(AuthContext);
  
  if (!user) {
    window.location.href = '/401';
  }

  if (!permissions) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        Loading...
      </div>
    );
  }

  const [opened, { open, close }] = useDisclosure(false);
  const dialerRef = useRef(null);
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const fullUrl = `${pathname}?${searchParams.toString()}`;
  const isLimitedAccess = tenant === 'DEMO' && user?.role === 'OPERASYON';
  
  let mockdata = [
    { label: 'Deneyim Dashboard', icon: IconHome, link: '/' + 'v2.0', active: fullUrl === '/' + 'v2.0' + '?' },
  ];

  if (
    permissions?.includes('Call.View') ||
    permissions?.includes('Call.Trend') ||
    permissions?.includes('Call.Search') ||
    permissions?.includes('Call.AgentView') ||
    permissions?.includes('Call.PromptTicketView') ||
    permissions?.includes('Call.PromptView')
  ) {
    let callLinks = [];
    if (permissions?.includes('Call.View')) {
      callLinks.push({
        label: 'Kayıtlar',
        icon: IconDatabase,
        link: '/' + 'v2.0' + '/channels?channelType=Call',
        active: fullUrl.includes('/channels?channelType=Call') && !fullUrl.includes('/channels/reports'),
      });
      callLinks.push({
        label: 'Raporlar',
        icon: IconFileAnalytics,
        link: '/' + 'v2.0' + '/channels/reports?channelType=Call',
        active: fullUrl.includes('/channels/reports?channelType=Call'),
      });
    }
    if (permissions?.includes('Call.AgentView')) {
      callLinks.push({
        label: 'MT Performans',
        icon: IconUserBolt,
        link: '/' + 'v2.0' + '/channels/agents?channelType=Call',
        active: fullUrl === '/' + 'v2.0' + '/channels/agents?channelType=Call',
      });
    }
    if (!isLimitedAccess) {
      if (permissions?.includes('Call.Search')) {
        callLinks.push({
          label: 'Kelime Sorgulama',
          icon: IconSearch,
          link: '/' + 'v2.0' + '/channels/search?channelType=Call',
          active: fullUrl === '/' + 'v2.0' + '/channels/search?channelType=Call',
        });
      }
      if (permissions?.includes('Call.Trend')) {
        callLinks.push({
          label: 'Trend Sorgulama',
          icon: IconTrendingUp,
          link: '/' + 'v2.0' + '/channels/trend?channelType=Call',
          active: fullUrl === '/' + 'v2.0' + '/channels/trend?channelType=Call',
        });
      }
    }
    if (!isLimitedAccess) {
      if (permissions?.includes('Call.PromptTicketView') || permissions?.includes('Call.PromptView')) {
        callLinks.push({
          label: 'Kalite Kuralları',
          icon: IconRuler,
          link: '/' + 'v2.0' + '/channels/qualityRules?channelType=Call',
          active: fullUrl === '/' + 'v2.0' + '/channels/qualityRules?channelType=Call',
        });
      }
    }
    if (permissions?.includes('Call.DemoCalling')) {
      callLinks.push({
        label: 'Demo Arama',
        icon: IconDeviceMobile,
        onclick: () => {
          dialerRef.current.openDialer();
        },
      });
    }
    mockdata.push({
      label: 'Çağrılar',
      icon: IconPhone,
      active: fullUrl.includes('?channelType=Call'),
      links: callLinks,
    });
  }

  if (
    !isLimitedAccess &&
    (permissions?.includes('Chat.View') ||
      permissions?.includes('Chat.Trend') ||
      permissions?.includes('Chat.Search') ||
      permissions?.includes('Chat.AgentView') ||
      permissions?.includes('Chat.PromptTicketView') ||
      permissions?.includes('Chat.PromptView'))
  ) {
    let chatLinks = [];
    if (permissions?.includes('Chat.View')) {
      chatLinks.push({
        label: 'Kayıtlar',
        icon: IconDatabase,
        link: '/' + 'v2.0' + '/channels?channelType=Chat',
        active: fullUrl.includes('/channels?channelType=Chat') && !fullUrl.includes('/channels/reports'),
      });
      chatLinks.push({
        label: 'Raporlar',
        icon: IconFileAnalytics,
        link: '/' + 'v2.0' + '/channels/reports?channelType=Chat',
        active: fullUrl.includes('/channels/reports?channelType=Chat'),
      });
    }
    if (permissions?.includes('Chat.AgentView')) {
      chatLinks.push({
        label: 'MT Performans',
        icon: IconUserBolt,
        link: '/' + 'v2.0' + '/channels/agents?channelType=Chat',
        active: fullUrl === '/' + 'v2.0' + '/channels/agents?channelType=Chat',
      });
    }
    if (permissions?.includes('Chat.Search')) {
      chatLinks.push({
        label: 'Kelime Sorgulama',
        icon: IconSearch,
        link: '/' + 'v2.0' + '/channels/search?channelType=Chat',
        active: fullUrl === '/' + 'v2.0' + '/channels/search?channelType=Chat',
      });
    }
    if (permissions?.includes('Chat.Trend')) {
      chatLinks.push({
        label: 'Trend Sorgulama',
        icon: IconTrendingUp,
        link: '/' + 'v2.0' + '/channels/trend?channelType=Chat',
        active: fullUrl === '/' + 'v2.0' + '/channels/trend?channelType=Chat',
      });
    }
    if (permissions?.includes('Chat.PromptTicketView') || permissions?.includes('Chat.PromptView')) {
      chatLinks.push({
        label: 'Kalite Kuralları',
        icon: IconRuler,
        link: '/' + 'v2.0' + '/channels/qualityRules?channelType=Chat',
        active: fullUrl === '/' + 'v2.0' + '/channels/qualityRules?channelType=Chat',
      });
    }
    mockdata.push({
      label: 'Yazışmalar',
      icon: IconMessage,
      active: fullUrl.includes('?channelType=Chat'),
      links: chatLinks,
    });
  }

  if (!isLimitedAccess && permissions?.includes('User.View')) {
    mockdata.push({
      label: 'Kullanıcılar',
      icon: IconUsers,
      active: fullUrl === '/' + 'v2.0' + '/users',
      link: '/' + 'v2.0' + '/users',
    });
  }
  if (!isLimitedAccess && permissions?.includes('Role.View')) {
    mockdata.push({
      label: 'Roller',
      icon: IconShield,
      active: fullUrl === '/' + 'v2.0' + '/roles',
      link: '/' + 'v2.0' + '/roles',
    });
  }

  return (
    <div style={{ display: 'flex' }}>
      <PhoneDialerModal ref={dialerRef} />
      <PluktoSearch />
      <nav style={{ width: isMenuShow ? '250px' : '100px' }} className={classes.navbar}>
        <div className={classes.header}>
          <Group style={{ justifyContent: 'center' }}>
            <Image src="/logo.svg" style={{ width: '100%' }} />
            <Text
              size="md"
              ta="center"
              mt={-5}
              mb={6}
              fw={500}
              style={{
                width: '100%',
                fontFamily: 'Poppins, sans-serif',
                background: 'linear-gradient(90deg, #14b8a6 0%, #06b6d4 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                letterSpacing: 1,
                fontSize: 13,
                textShadow: '0 2px 16px #99f6e4',
                transition: 'all 0.3s',
                fontStyle: 'italic',
                lineHeight: 1.2,
                padding: '0.2em 0',
                borderRadius: 8,
                boxShadow: '0 2px 12px 0 rgba(20,184,166,0.10)',
              }}
            >
              Müşteri Deneyim Platformu
            </Text>
            <Code fw={700}>{'v2.0'}</Code>
            <ActionIcon size={'md'} onClick={() => setMenuShow(!isMenuShow)}>
              {isMenuShow ? <IconChevronLeft size={18} /> : <IconChevronRight size={16} />}
            </ActionIcon>
            <Tooltip label="Ara (Ctrl+K)" position="right">
              <ActionIcon
                variant="light"
                color="teal"
                size="lg"
                radius="md"
                aria-label="Ara"
                onClick={() => spotlight.open()}
              >
                <IconSearch size={18} />
              </ActionIcon>
            </Tooltip>
          </Group>
        </div>

        <div className={classes.footer}>
          <Menu position="right">
            <Menu.Target>
              <UnstyledButton className={classes2.user}>
                <Group style={{ justifyContent: 'center' }}>
                  {isMenuShow ? (
                    <>
                      <Avatar color="teal" radius="xl">
                        {user.name[0]}
                        {user.surname[0]}
                      </Avatar>
                      <div style={{ flex: 1 }}>
                        <Text size="sm" fw={500}>
                          {user.name + ' ' + user.surname}
                        </Text>
                        <Text c="dimmed" size="xs">
                          {user.email}
                        </Text>
                      </div>
                      <IconChevronRight size={14} stroke={1.5} />
                    </>
                  ) : (
                    <>
                      <Tooltip label={user.name + ' ' + user.surname}>
                        <Avatar color="teal" radius="xl">
                          {user.name[0]}
                          {user.surname[0]}
                        </Avatar>
                      </Tooltip>
                    </>
                  )}
                </Group>
              </UnstyledButton>
            </Menu.Target>
            <Menu.Dropdown style={{ width: '200px' }}>
              <Pill mb="sm" style={{ justifyContent: 'center', width: '100%' }}>
                {user.role}
              </Pill>
              <Select
                comboboxProps={{ position: 'left' }}
                value={tenant}
                disabled={tenants.length === 1}
                onChange={async (_value) => {
                  if (_value === null) return;
                  const response = await fetchAuthClient('Auth/me/tenant/' + _value, { method: 'GET' });
                  if (response.ok) {
                    const data = await response.json();
                    localStorage.setItem('accessToken', data.accessToken);
                    localStorage.setItem('refreshToken', data.refreshToken);
                    window.location.href = '/' + 'v2.0';
                  }
                }}
                data={tenants}
              />
              <Menu.Divider mt="sm" mb="sm" />
              <Menu.Item onClick={() => open()}>Profil</Menu.Item>
              <Menu.Item color="red" component={Link} href={'/logout'}>
                Çıkış Yap
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
        </div>
        <ScrollArea className={classes.links}>
          <div>
            {mockdata.map((x, index) => (
              <Tooltip label={x.label} position="right" transitionProps={{ duration: 0 }} key={index}>
                <NavLink
                  color="black"
                  href={x.link}
                  label={isMenuShow ? x.label : ''}
                  active={x.active}
                  defaultOpened={x.active}
                  leftSection={<x.icon />}
                  childrenOffset={28}
                >
                  {x.links && (
                    <>
                      {x.links.map((y, index2) => (
                        <Tooltip
                          label={y.label}
                          position="right"
                          transitionProps={{ duration: 0 }}
                          key={index + ' ' + index2}
                        >
                          <NavLink
                            active={y.active}
                            label={isMenuShow ? y.label : ''}
                            href={y.link}
                            onClick={y.onclick ? y.onclick : undefined}
                            leftSection={<y.icon />}
                            color="teal"
                          />
                        </Tooltip>
                      ))}
                    </>
                  )}
                </NavLink>
              </Tooltip>
            ))}
          </div>
        </ScrollArea>
        <Drawer opened={opened} onClose={close}>
          <ProfileForm />
        </Drawer>
      </nav>
      <div
        style={{
          width: isMenuShow ? 'calc(100% - 250px)' : 'calc(100% - 100px)',
          padding: '16px',
          position: 'relative',
        }}
      >
        {children}
      </div>
    </div>
  );
}
