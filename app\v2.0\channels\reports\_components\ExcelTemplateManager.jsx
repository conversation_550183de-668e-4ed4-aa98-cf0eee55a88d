import React, { useState, useContext } from 'react';
import {
  <PERSON>dal,
  <PERSON>ton,
  Stack,
  Group,
  Text,
  Title,
  Checkbox,
  Paper,
  Badge,
  Divider,
  Alert,
  Loader,
  Flex,
  Select,
  MultiSelect,
} from '@mantine/core';
import {
  IconFileExport,
  IconSettings,
  IconInfoCircle,
  IconDownload,
} from '@tabler/icons-react';
import * as ExcelJS from 'exceljs';
import { AuthContext } from '@/common/contexts/AuthContext';

const ExcelTemplateManager = ({ 
  channelType, 
  tenantParameters, 
  agents, 
  onTemplateExport,
  opened,
  onClose 
}) => {
  const { fetchAuthClient } = useContext(AuthContext);
  const [loading, setLoading] = useState(false);
  const [selectedSheets, setSelectedSheets] = useState([]);
  const [selectedColumns, setSelectedColumns] = useState({});
  const [exportType, setExportType] = useState('custom'); // 'custom' or 'template'
  const [selectedTemplate, setSelectedTemplate] = useState('');
  const [pageSize, setPageSize] = useState(5000);

  // Mevcut sheet'ler ve column'ları tanımlayalım
  const availableSheets = [
    {
      id: 'quality_analysis',
      name: 'Kalite Analizi',
      description: 'Ana veri tablosu - tüm kayıtlar ve kalite puanları',
      color: 'blue',
      columns: [
        { id: 'channel', name: 'Kanal', required: true },
        { id: 'date', name: 'Tarih', required: false },
        { id: 'time', name: 'Saat', required: false },
        { id: 'identifier', name: 'Kimlik', required: false },
        { id: 'agent', name: 'Temsilci', required: false },
        { id: 'duration', name: 'Süre', required: false },
        { id: 'point', name: 'Puan', required: false },
        { id: 'vendor', name: 'Firma', required: false },
        { id: 'category', name: 'Kategori', required: false },
        { id: 'quality_criteria', name: 'Kalite Kriterleri', required: false },
      ]
    },
    {
      id: 'agent_stats',
      name: 'Temsilci İstatistikleri',
      description: 'Agent performans verileri ve firma bazlı gruplamalar',
      color: 'green',
      columns: [
        { id: 'agent_name', name: 'Temsilci Adı', required: true },
        { id: 'company', name: 'Firma', required: true },
        { id: 'call_count', name: 'Çağrı Adedi', required: false },
        { id: 'avg_duration', name: 'Ortalama Süre', required: false },
        { id: 'avg_point', name: 'Ortalama Puan', required: false },
        { id: 'avg_silence', name: 'Ortalama Sessizlik', required: false },
        { id: 'quality_success_rates', name: 'Kalite Başarı Oranları', required: false },
      ]
    },
    {
      id: 'category_stats',
      name: 'Kategori İstatistikleri',
      description: 'Kategori bazlı analizler ve firma karşılaştırmaları',
      color: 'orange',
      columns: [
        { id: 'company', name: 'Firma', required: true },
        { id: 'category', name: 'Kategori', required: true },
        { id: 'call_count', name: 'Çağrı Adedi', required: false },
        { id: 'avg_duration', name: 'Ortalama Süre', required: false },
        { id: 'avg_point', name: 'Ortalama Puan', required: false },
        { id: 'quality_rates', name: 'Kalite Oranları', required: false },
      ]
    },
    {
      id: 'quality_rules_stats',
      name: 'Kalite Kuralı İstatistikleri',
      description: 'Kural bazlı performans ve başarı oranları',
      color: 'purple',
      columns: [
        { id: 'rule_name', name: 'Kural Adı', required: true },
        { id: 'total_count', name: 'Toplam Adet', required: false },
        { id: 'success_count', name: 'Başarı Adedi', required: false },
        { id: 'success_rate', name: 'Başarı Oranı', required: false },
        { id: 'avg_point', name: 'Ortalama Puan', required: false },
        { id: 'avg_duration', name: 'Ortalama Süre', required: false },
      ]
    },
    {
      id: 'weekly_analysis',
      name: 'Haftalık Çağrı Analizi',
      description: 'Zaman bazlı analizler ve haftalık performans',
      color: 'red',
      columns: [
        { id: 'week_period', name: 'Hafta Dönemi', required: true },
        { id: 'day_hour', name: 'Gün/Saat', required: true },
        { id: 'call_count', name: 'Çağrı Adedi', required: false },
        { id: 'avg_point', name: 'Ortalama Puan', required: false },
        { id: 'total_duration', name: 'Toplam Süre', required: false },
      ]
    }
  ];

  // Hazır template'ler
  const predefinedTemplates = [
    {
      id: 'daily_agent_analysis',
      name: 'Günlük Temsilci Bazlı Analiz Raporu',
      description: 'Temsilci bazında günlük analiz adedi ve kalite puanı tablosu',
      sheets: ['agent_stats'],
      columns: {
        agent_stats: ['agent_name', 'company', 'call_count', 'avg_point']
      }
    }
  ];

  const handleSheetSelection = (sheetId, checked) => {
    if (checked) {
      setSelectedSheets([...selectedSheets, sheetId]);
      // Sheet seçildiğinde required column'ları otomatik seç
      const sheet = availableSheets.find(s => s.id === sheetId);
      const requiredColumns = sheet.columns.filter(col => col.required).map(col => col.id);
      setSelectedColumns(prev => ({
        ...prev,
        [sheetId]: requiredColumns
      }));
    } else {
      setSelectedSheets(selectedSheets.filter(id => id !== sheetId));
      setSelectedColumns(prev => {
        const newColumns = { ...prev };
        delete newColumns[sheetId];
        return newColumns;
      });
    }
  };

  const handleColumnSelection = (sheetId, columnIds) => {
    setSelectedColumns(prev => ({
      ...prev,
      [sheetId]: columnIds
    }));
  };

  const handleCustomExport = async () => {
    if (selectedSheets.length === 0) {
      alert('Lütfen en az bir sheet seçin.');
      return;
    }

    setLoading(true);
    try {
      // Custom export logic buraya gelecek
      await onTemplateExport({
        type: 'custom',
        sheets: selectedSheets,
        columns: selectedColumns,
        pageSize: pageSize
      });
    } catch (error) {
      console.error('Custom export error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTemplateExport = async (templateId) => {
    const template = predefinedTemplates.find(t => t.id === templateId);
    if (!template) return;

    setLoading(true);
    try {
      await onTemplateExport({
        type: 'template',
        template: template,
        pageSize: pageSize
      });
    } catch (error) {
      console.error('Template export error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={<Title order={4}>Excel Rapor Oluşturucu</Title>}
      size="xl"
      padding="lg"
      centered
    >
      {loading ? (
        <Flex direction="column" align="center" gap="md" p="xl">
          <Loader size="lg" variant="dots" />
          <Text size="sm" fw={500}>
            Excel dosyası hazırlanıyor, lütfen bekleyin...
          </Text>
        </Flex>
      ) : (
        <Stack gap="lg">
          {/* Export Type Selection */}
          <Paper p="md" withBorder radius="sm">
            <Group mb="md">
              <Title order={5}>Rapor Türü Seçin</Title>
            </Group>
            <Group gap="lg" mb="md">
              <Button
                variant={exportType === 'template' ? 'filled' : 'light'}
                onClick={() => setExportType('template')}
                leftSection={<IconFileExport size={16} />}
              >
                Hazır Şablon
              </Button>
              <Button
                variant={exportType === 'custom' ? 'filled' : 'light'}
                onClick={() => setExportType('custom')}
                leftSection={<IconSettings size={16} />}
              >
                Özel Rapor
              </Button>
            </Group>

            {/* Sayfa Sayısı Seçimi */}
            <Group gap="md">
              <Text size="sm" fw={500}>Maksimum Kayıt Sayısı:</Text>
              <Select
                value={pageSize.toString()}
                onChange={(value) => setPageSize(parseInt(value))}
                data={[
                  { value: '1000', label: '1.000 kayıt' },
                  { value: '5000', label: '5.000 kayıt' },
                  { value: '10000', label: '10.000 kayıt' },
                  { value: '25000', label: '25.000 kayıt' },
                  { value: '50000', label: '50.000 kayıt' },
                ]}
                size="sm"
                w={150}
              />
              <Text size="xs" c="dimmed">
                Daha fazla kayıt daha uzun sürebilir
              </Text>
            </Group>
          </Paper>

          {exportType === 'template' && (
            <Paper p="md" withBorder radius="sm">
              <Title order={5} mb="md">Hazır Şablonlar</Title>
              <Stack gap="md">
                {predefinedTemplates.map((template) => (
                  <Paper key={template.id} p="md" withBorder radius="sm" bg="blue.0">
                    <Group justify="space-between" align="flex-start">
                      <div style={{ flex: 1 }}>
                        <Text fw={500} size="sm" mb="xs">
                          {template.name}
                        </Text>
                        <Text size="xs" c="dimmed" mb="sm">
                          {template.description}
                        </Text>
                        <Group gap="xs">
                          {template.sheets.map(sheetId => {
                            const sheet = availableSheets.find(s => s.id === sheetId);
                            return (
                              <Badge key={sheetId} size="xs" variant="light" color={sheet?.color}>
                                {sheet?.name}
                              </Badge>
                            );
                          })}
                        </Group>
                      </div>
                      <Button
                        size="xs"
                        variant="light"
                        leftSection={<IconDownload size={14} />}
                        loading={loading}
                        onClick={() => handleTemplateExport(template.id)}
                      >
                        İndir
                      </Button>
                    </Group>
                  </Paper>
                ))}
              </Stack>
            </Paper>
          )}

          {exportType === 'custom' && (
            <>
              <Paper p="md" withBorder radius="sm">
                <Title order={5} mb="md">Dahil Edilecek Veri Kaynakları</Title>
                <Stack gap="md">
                  {availableSheets.map((sheet) => (
                    <Paper key={sheet.id} p="md" withBorder radius="sm" bg={selectedSheets.includes(sheet.id) ? `${sheet.color}.0` : 'gray.0'}>
                      <Group align="flex-start" gap="md">
                        <Checkbox
                          checked={selectedSheets.includes(sheet.id)}
                          onChange={(e) => handleSheetSelection(sheet.id, e.currentTarget.checked)}
                          size="md"
                        />
                        <div style={{ flex: 1 }}>
                          <Group gap="xs" mb="xs">
                            <Badge size="sm" color={sheet.color}>{sheet.name}</Badge>
                          </Group>
                          <Text size="xs" c="dimmed" mb="sm">
                            {sheet.description}
                          </Text>
                          
                          {selectedSheets.includes(sheet.id) && (
                            <MultiSelect
                              label="Dahil edilecek sütunlar"
                              placeholder="Sütun seçin"
                              data={sheet.columns.map(col => ({
                                value: col.id,
                                label: col.name + (col.required ? ' (Zorunlu)' : '')
                              }))}
                              value={selectedColumns[sheet.id] || []}
                              onChange={(values) => handleColumnSelection(sheet.id, values)}
                              size="xs"
                              mt="xs"
                            />
                          )}
                        </div>
                      </Group>
                    </Paper>
                  ))}
                </Stack>
              </Paper>

              <Group justify="flex-end">
                <Button
                  variant="light"
                  onClick={onClose}
                >
                  İptal
                </Button>
                <Button
                  leftSection={<IconDownload size={16} />}
                  onClick={handleCustomExport}
                  disabled={selectedSheets.length === 0}
                >
                  Özel Raporu İndir
                </Button>
              </Group>
            </>
          )}

          <Alert icon={<IconInfoCircle size={16} />} color="blue" variant="light">
            <Text size="sm">
              Raporlar mevcut filtrelerinize göre oluşturulacaktır. 
              Ana kayıtlar sayfasındaki filtreleri kullanarak veri kapsamını daraltabilirsiniz.
            </Text>
          </Alert>
        </Stack>
      )}
    </Modal>
  );
};

export default ExcelTemplateManager;
