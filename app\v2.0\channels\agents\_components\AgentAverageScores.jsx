import React, { useState } from 'react';
import {
  Avatar,
  Badge,
  Box,
  Button,
  Card,
  Group,
  Modal,
  Paper,
  Progress,
  ScrollArea,
  SimpleGrid, Stack,
  Tabs,
  Text,
  Tooltip,
  useMantineTheme,
  ThemeIcon
} from '@mantine/core';
import {
  IconArrowDown,
  IconArrowUp,
  IconChevronRight,
  IconList,
  IconTarget,
  IconUserCircle,
  IconUsers
} from '@tabler/icons-react';

const AgentProgressBar = ({ agent, targetQualityPoint, onClick }) => {
  const theme = useMantineTheme();
  
  const getProgressColor = (score, target) => {
    const diff = score - target;
    if (diff >= 5) return theme.colors.green[6];
    if (diff >= 0) return theme.colors.blue[6];
    if (diff >= -10) return theme.colors.yellow[6];
    return theme.colors.red[6];
  };
  
  const progressValue = Math.min(Math.round((agent.score / targetQualityPoint) * 100), 100);
  const color = getProgressColor(agent.score, targetQualityPoint);
  
  return (
    <Paper withBorder p="xs" radius="md" onClick={onClick} style={{ cursor: 'pointer' }}>
      <Group position="apart" mb="xs">
        <Group spacing="xs">
          <Avatar size="sm" radius="xl" color={color}>{agent.name.substring(0, 2).toUpperCase()}</Avatar>
          <Text size="sm" fw={500} lineClamp={1}>{agent.name}</Text>
        </Group>
        <Badge
          color={color}
          variant="light"
        >
          {agent.score?.toLocaleString('tr-TR', { minimumFractionDigits: 1, maximumFractionDigits: 1 })}
        </Badge>
      </Group>
      <Progress 
        value={progressValue} 
        color={color}
        size="md" 
        radius="sm"
        stripes={agent.score < targetQualityPoint}
      />
    </Paper>
  );
};

const AgentDetailModal = ({ agent, targetQualityPoint, opened, onClose }) => {
  const theme = useMantineTheme();
  
  if (!agent) return null;
  
  const getStatusColor = (score, target) => {
    const diff = score - target;
    if (diff >= 5) return theme.colors.green[6];
    if (diff >= 0) return theme.colors.blue[6];
    if (diff >= -10) return theme.colors.yellow[6];
    return theme.colors.red[6];
  };
  
  const getStatusText = (score, target) => {
    const diff = score - target;
    if (diff >= 5) return "Hedefi Aşıyor";
    if (diff >= 0) return "Hedefe Ulaşıldı";
    if (diff >= -10) return "Hedefe Yakın";
    return "Hedefin Altında";
  };
  
  const color = getStatusColor(agent.score, targetQualityPoint);
  const statusText = getStatusText(agent.score, targetQualityPoint);
  const progressValue = Math.min(Math.round((agent.score / targetQualityPoint) * 100), 100);
  
  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={
        <Group>
          <IconUserCircle size={24} />
          <Text fw={700}>Temsilci Detayı</Text>
        </Group>
      }
      size="md"
    >
      <Group position="apart" mb="md">
        <Group>
          <Avatar size="lg" radius="xl" color={color}>{agent.name.substring(0, 2).toUpperCase()}</Avatar>
          <div>
            <Text fw={700} size="lg">{agent.name}</Text>
            <Badge color={color} variant="light">{statusText}</Badge>
          </div>
        </Group>
        <Box>
          <Text fw={700} size="xl" ta="right">{agent.score?.toLocaleString('tr-TR', { minimumFractionDigits: 1, maximumFractionDigits: 1 })}</Text>
          <Text size="xs" c="dimmed" ta="right">toplam puan</Text>
        </Box>
      </Group>
      
      <Paper withBorder p="md" radius="md" mb="md">
        <Text size="sm" fw={600} mb="xs">Hedef İlerlemesi</Text>
        <Group position="apart" mb="xs">
          <Text size="sm">Hedef: {targetQualityPoint?.toLocaleString('tr-TR')}</Text>
          <Text size="sm" fw={500} color={color}>
            {agent.score > targetQualityPoint ? '+' : ''}{(agent.score - targetQualityPoint)?.toLocaleString('tr-TR', { minimumFractionDigits: 1, maximumFractionDigits: 1 })}
          </Text>
        </Group>
        <Progress 
          value={progressValue} 
          color={color}
          size="xl" 
          radius="md"
          stripes={agent.score < targetQualityPoint}
          mb="xs"
        />
        <Text size="xs" c="dimmed" ta="center">
          {agent.score >= targetQualityPoint
            ? `Hedefi ${(agent.score - targetQualityPoint)?.toLocaleString('tr-TR', { minimumFractionDigits: 1, maximumFractionDigits: 1 })} puan aşıyor`
            : `Hedefe ulaşmak için ${(targetQualityPoint - agent.score)?.toLocaleString('tr-TR', { minimumFractionDigits: 1, maximumFractionDigits: 1 })} puan gerekiyor`}
        </Text>
      </Paper>
      
      {agent.details && (
        <Paper withBorder p="md" radius="md">
          <Text size="sm" fw={600} mb="xs">Performans Detayları</Text>
          {Object.entries(agent.details).map(([key, value]) => (
            <Group key={key} position="apart" mb="xs">
              <Text size="sm">{key}</Text>
              <Badge>{value}</Badge>
            </Group>
          ))}
        </Paper>
      )}
      
      <Button fullWidth mt="md" onClick={onClose}>Kapat</Button>
    </Modal>
  );
};

const AgentsListModal = ({ agents, targetQualityPoint, opened, onClose, onAgentClick, activeTab = 'all' }) => {
  const theme = useMantineTheme();
  const [activeTabState, setActiveTabState] = useState(activeTab);
  
  React.useEffect(() => {
    if (opened) {
      setActiveTabState(activeTab);
    }
  }, [opened, activeTab]);
  
  if (!agents || agents.length === 0) return null;
  
  const sortedAgents = [...agents].sort((a, b) => b.score - a.score);
  const aboveTargetAgents = sortedAgents.filter(a => a.score >= targetQualityPoint);
  const belowTargetAgents = sortedAgents.filter(a => a.score < targetQualityPoint);
  
  let agentsToShow;
  if (activeTabState === 'all') {
    agentsToShow = sortedAgents;
  } else if (activeTabState === 'above') {
    agentsToShow = aboveTargetAgents;
  } else if (activeTabState === 'below') {
    agentsToShow = belowTargetAgents;
  } else {
    agentsToShow = sortedAgents;
  }
  
  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={
        <Group>
          <IconUsers size={24} />
          <Text fw={700}>Temsilci Performansları</Text>
        </Group>
      }
      size="lg"
    >
    
      
      <Tabs value={activeTabState} onChange={setActiveTabState} mb="md">
        <Tabs.List grow>
          <Tabs.Tab 
            value="all" 
            icon={<IconList size={16} />}
            rightSection={<Badge size="sm" variant="filled" color="blue">{agents.length?.toLocaleString('tr-TR')}</Badge>}
          >
            Tüm Temsilciler
          </Tabs.Tab>
          <Tabs.Tab 
            value="above" 
            icon={<IconArrowUp size={16} />}
            rightSection={<Badge size="sm" variant="filled" color="green">{aboveTargetAgents.length?.toLocaleString('tr-TR')}</Badge>}
          >
            Hedef Üzerinde
          </Tabs.Tab>
          <Tabs.Tab 
            value="below" 
            icon={<IconArrowDown size={16} />}
            rightSection={<Badge size="sm" variant="filled" color="red">{belowTargetAgents.length?.toLocaleString('tr-TR')}</Badge>}
          >
            Hedef Altında
          </Tabs.Tab>
        </Tabs.List>
      </Tabs>
      
      <ScrollArea h={400} type="auto" offsetScrollbars>
        {agentsToShow.length > 0 ? (
          <Stack spacing="xs">
            {agentsToShow.map((agent, index) => (
              <AgentProgressBar 
                key={index}
                agent={agent}
                targetQualityPoint={targetQualityPoint}
                onClick={() => onAgentClick(agent)}
              />
            ))}
          </Stack>
        ) : (
          <Text ta="center" c="dimmed" fz="sm" mt="md">
            {activeTabState === 'above' 
              ? 'Hedefin üzerinde temsilci bulunmamaktadır.' 
              : activeTabState === 'below' 
                ? 'Hedefin altında temsilci bulunmamaktadır.' 
                : 'Temsilci bulunmamaktadır.'}
          </Text>
        )}
      </ScrollArea>
      
      <Paper withBorder p="md" radius="md" mt="md">
        <Group position="apart">
          <Group>
           
          </Group>
          <Text c="dimmed" fz="sm">
            Hedefe Ulaşma Oranı: %{((aboveTargetAgents.length / agents.length) * 100)?.toLocaleString('tr-TR', { minimumFractionDigits: 1, maximumFractionDigits: 1 })}
          </Text>
        </Group>
      </Paper>
      
      <Button fullWidth mt="md" onClick={onClose}>Kapat</Button>
    </Modal>
  );
};

const AgentAverageScores = ({ agentPointListObj, targetQualityPoint = 80 }) => {
  const theme = useMantineTheme();
  const [selectedAgent, setSelectedAgent] = useState(null);
  const [modalOpened, setModalOpened] = useState(false);
  const [agentsListModalOpened, setAgentsListModalOpened] = useState(false);
  const [activeListTab, setActiveListTab] = useState('all');

  if (!agentPointListObj || agentPointListObj.length === 0) {
    return (
      <Card withBorder p="md" radius="md" shadow="sm">
        <Text c="dimmed" size="xl" tt="uppercase" fw={700}>
          Temsilci Puan İstatistikleri
        </Text>
        <Text mt="md" ta="center" c="dimmed" fz="sm">
          Veri bulunamadı
        </Text>
      </Card>
    );
  }

  const sortedAgents = [...agentPointListObj].sort((a, b) => b.score - a.score);
  
  const averageScore = agentPointListObj.reduce((sum, agent) => sum + agent.score, 0) / agentPointListObj.length;
  
  const meetingTarget = agentPointListObj.filter(agent => agent.score >= targetQualityPoint).length;
  const belowTarget = agentPointListObj.filter(agent => agent.score < targetQualityPoint).length;
  
  const totalAgents = agentPointListObj.length;
  const meetingTargetPercentage = (meetingTarget / totalAgents) * 100;
  
  const targetProgress = Math.min(Math.round((averageScore / targetQualityPoint) * 100), 100);
  
  const getStatusColor = (average, target) => {
    const diff = average - target;
    if (diff >= 5) return theme.colors.green[6]; 
    if (diff >= 0) return theme.colors.blue[6];  
    if (diff >= -10) return theme.colors.yellow[6]; 
    return theme.colors.red[6]; 
  };
  
  const statusColor = getStatusColor(averageScore, targetQualityPoint);
  
  const getStatusText = (average, target) => {
    const diff = average - target;
    if (diff >= 5) return "Hedefi Aşıyor";
    if (diff >= 0) return "Hedefe Ulaşıldı";
    if (diff >= -10) return "Hedefe Yakın";
    return "Hedefin Altında";
  };
  
  const statusText = getStatusText(averageScore, targetQualityPoint);
  
  const generateInsight = (average, target, meetingPercentage) => {
    if (average >= target) {
      if (meetingPercentage < 70) {
        return "Ortalama hedefi karşılasa da, birçok temsilci hedefin altında kalıyor. Eğitim ve performans geliştirme için fırsat var.";
      }
      return "Ekip genel olarak hedefi karşılıyor. Performansı sürdürmek ve geliştirmek için çalışmalar devam etmeli.";
    } else {
      if (meetingPercentage < 30) {
        return "Ekibin büyük çoğunluğu hedefin altında. Kapsamlı eğitim ve performans iyileştirme çalışmaları gerekiyor.";
      }
      return "Ortalama hedefin altında. Düşük performans gösteren temsilcilere odaklanılmalı.";
    }
  };

  const insight = generateInsight(averageScore, targetQualityPoint, meetingTargetPercentage);

  const handleAgentClick = (agent) => {
    setSelectedAgent(agent);
    setModalOpened(true);
  };

  const handleOpenAgentsList = (activeTab = 'all') => {
    setActiveListTab(activeTab);
    setAgentsListModalOpened(true);
  };

  return (
    <Card withBorder p="md" radius="md" shadow="sm" style={{ height: '100%' }}>
      <Group position="apart" mb="md">
        <div className='flex w-full justify-between'>
          <Text c="dimmed" size="xl" tt="uppercase" fw={700}>
            Temsilci Puan İstatistikleri
          </Text>
          <Tooltip
            label="Hedef kalite puanı"
            position="top"
            withArrow
          >
            <Badge
              leftSection={<IconTarget size={14} />}
              size="lg"
              variant="outline"
            >
              Hedef: {targetQualityPoint?.toLocaleString('tr-TR')}
            </Badge>
          </Tooltip>
        </div>
      </Group>
      
      <SimpleGrid cols={{ base: 1, sm: 1 }} spacing="lg">
        <Box>
          <Paper withBorder p="lg" radius="md" shadow="sm">
            <Group position="apart" mb="lg">
              <Box
                style={{
                  textAlign: 'center',
                  backgroundColor: `${statusColor}15`,
                  borderRadius: theme.radius.lg,
                  padding: '12px 20px',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
                  width: '40%',
                }}
              >
                <Text fz="2.5rem" fw={800} style={{ lineHeight: 1, letterSpacing: '-0.5px' }}>
                  {averageScore?.toLocaleString('tr-TR', { minimumFractionDigits: 1, maximumFractionDigits: 1 })}
                </Text>
                <Text fz="sm" fw={500} c="dimmed">ortalama puan</Text>
              </Box>

              <Box 
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'flex-end',
                  gap: '8px',
                  width: '50%'
                }}
              >
                <Badge 
                  color={statusColor} 
                  variant="light" 
                  size="lg" 
                  radius="md" 
                  px="md" 
                  py="sm"
                  style={{ fontSize: '1rem', fontWeight: 600 }}
                >
                  {statusText}
                </Badge>
                <Group spacing="xs">
                  <ThemeIcon 
                    size="xl" 
                    radius="xl" 
                    color={statusColor} 
                    variant="light"
                    style={{ boxShadow: `0 0 10px ${statusColor}40` }}
                  >
                    {averageScore > targetQualityPoint ?
                      <IconArrowUp size={24} /> :
                      averageScore === targetQualityPoint ?
                        <IconTarget size={24} /> :
                        <IconArrowDown size={24} />
                    }
                  </ThemeIcon>
                  <Text fz="sm" fw={500} c="dimmed">
                    {averageScore > targetQualityPoint 
                      ? 'Hedef üzerinde performans' 
                      : averageScore === targetQualityPoint 
                        ? 'Hedefte' 
                        : 'Gelişim gerekiyor'}
                  </Text>
                </Group>
              </Box>
            </Group>

            <Box mb="md" mt="lg">
              <Group position="apart" mb={8}>
                <div className='flex w-full justify-between'>
                  <Text fz="sm" fw={500}>Hedef: {targetQualityPoint?.toLocaleString('tr-TR')}</Text>
                  <Text
                    fz="sm"
                    fw={600}
                    color={statusColor}
                    style={{
                      backgroundColor: `${statusColor}15`,
                      padding: '2px 10px',
                      borderRadius: theme.radius.sm
                    }}
                  >
                    {averageScore > targetQualityPoint ? '+' : ''}{(averageScore - targetQualityPoint)?.toLocaleString('tr-TR', { minimumFractionDigits: 1, maximumFractionDigits: 1 })}
                  </Text>
                </div>
              </Group>
              <Progress 
                value={targetProgress} 
                color={statusColor}
                size="xl"
                radius="xl"
                stripes={averageScore < targetQualityPoint}
                mb={8}
                style={{ height: '12px' }}
              />
              <Text fz="xs" c="dimmed" ta="center">
                {averageScore >= targetQualityPoint
                  ? `Ekip hedefi ${(averageScore - targetQualityPoint)?.toLocaleString('tr-TR', { minimumFractionDigits: 1, maximumFractionDigits: 1 })} puan aşıyor`
                  : `Hedefe ulaşmak için ${(targetQualityPoint - averageScore)?.toLocaleString('tr-TR', { minimumFractionDigits: 1, maximumFractionDigits: 1 })} puan gerekiyor`}
              </Text>
            </Box>

            <Button 
              fullWidth
              mt="md"
              size="md"
              variant="light" 
              color={statusColor}
              rightIcon={<IconChevronRight size={18} />}
              onClick={() => handleOpenAgentsList('all')}
              style={{ 
                boxShadow: `0 2px 4px ${statusColor}20`,
                fontWeight: 600,
                transition: 'transform 0.2s, box-shadow 0.2s',
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: `0 4px 8px ${statusColor}30`
                }
              }}
            >
              Tüm Temsilcileri Görüntüle
            </Button>
          </Paper>

          <Box my="md">
            <div
              style={{
                borderTop: `1px solid ${theme.colors.gray[3]}`,
                margin: '16px 0',
                width: '100%',
              }}
            />
          </Box>
          
          <SimpleGrid cols={2} mt="md" spacing="md">
            <Paper 
              p="md" 
              withBorder 
              radius="md" 
              style={{ 
                borderLeft: `4px solid ${theme.colors.green[6]}`,
                background: `linear-gradient(to right, rgba(220, 252, 231, 0.4), rgba(220, 252, 231, 0.1))`,
                cursor: 'pointer' 
              }}
              onClick={() => handleOpenAgentsList('above')}
            >
              <Group position="apart" noWrap>
                <Box>
                  <Text size="xs" c="dimmed" mb={5}>Hedef Üzerinde</Text>
                  <Text fw={700} size="xl">{agentPointListObj.filter(a => a.score >= targetQualityPoint).length?.toLocaleString('tr-TR')}</Text>
                  <Text size="xs" c="dimmed">temsilci</Text>
                </Box>
                <ThemeIcon 
                  size={48} 
                  radius="xl" 
                  variant="light" 
                  color="green"
                  style={{ opacity: 0.8 }}
                >
                  <IconArrowUp size={24} />
                </ThemeIcon>
              </Group>
            </Paper>
            
            <Paper 
              p="md" 
              withBorder 
              radius="md" 
              style={{ 
                borderLeft: `4px solid ${theme.colors.red[6]}`,
                background: `linear-gradient(to right, rgba(254, 226, 226, 0.4), rgba(254, 226, 226, 0.1))`,
                cursor: 'pointer'
              }}
              onClick={() => handleOpenAgentsList('below')}
            >
              <Group position="apart" noWrap>
                <Box>
                  <Text size="xs" c="dimmed" mb={5}>Hedefin Altında</Text>
                  <Text fw={700} size="xl">{agentPointListObj.filter(a => a.score < targetQualityPoint).length?.toLocaleString('tr-TR')}</Text>
                  <Text size="xs" c="dimmed">temsilci</Text>
                </Box>
                <ThemeIcon 
                  size={48} 
                  radius="xl" 
                  variant="light" 
                  color="red"
                  style={{ opacity: 0.8 }}
                >
                  <IconArrowDown size={24} />
                </ThemeIcon>
              </Group>
            </Paper>
          </SimpleGrid>
        </Box>
      </SimpleGrid>
      
      <AgentDetailModal 
        agent={selectedAgent}
        targetQualityPoint={targetQualityPoint}
        opened={modalOpened}
        onClose={() => setModalOpened(false)}
      />
      
      <AgentsListModal
        agents={agentPointListObj}
        targetQualityPoint={targetQualityPoint}
        opened={agentsListModalOpened}
        onClose={() => setAgentsListModalOpened(false)}
        onAgentClick={handleAgentClick}
        activeTab={activeListTab}
      />
    </Card>
  );
};

export default AgentAverageScores;
