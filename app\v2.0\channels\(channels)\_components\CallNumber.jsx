import React from 'react';
import { Box, Card, Group, HoverCard, SimpleGrid, Text } from '@mantine/core';
import { IconMessage, IconPhoneCall } from '@tabler/icons-react';

const CallNumber = ({ setIsAnalysisCompletedFilterValue, setIsAgentActiveFilterValue, setFilter, dashboardData, channelType, label, classes }) => {
  return (
    <Card withBorder p="md" radius="md" style={{ height: '100%' }}>
      <HoverCard width={350} shadow="md" withinPortal position="bottom">
        <HoverCard.Target>
          <Box style={{ cursor: 'pointer' }} className='flex flex-col justify-between h-full'>
            <Text c="dimmed" size="xs" tt="uppercase" fw={700}>
              {label} Sayısı
            </Text>
            <Group justify="space-between" style={{ marginTop: 'auto' }}>
              <Group align="flex-end" gap="xs">
                <Text fz="lg" fw={700}>
                  {dashboardData.totalCount.toLocaleString('tr-TR')}
                </Text>
              </Group>
              {channelType === 'Call' && <IconPhoneCall size={20} className={classes.icon} stroke={1.5} />}
              {channelType === 'Chat' && <IconMessage size={20} className={classes.icon} stroke={1.5} />}
            </Group>
          </Box>
        </HoverCard.Target>
        <HoverCard.Dropdown p="xs">
          <Text fw={600} size="sm" mb="xs">
            Detaylı İstatistikler
          </Text>
          <SimpleGrid cols={{ base: 1, md: 2 }}>
            <Box
              style={{
                borderBottomColor: 'teal',
                display: 'flex',
                flexDirection: 'column',
                cursor: 'pointer',
              }}
              className={classes.stat}
              onClick={() => {
                setFilter('true', false, setIsAnalysisCompletedFilterValue, 'isAnalysisCompleted');
              }}
            >
              <Text tt="uppercase" fz="xs" c="dimmed" fw={700}>
                Analizi Tamamlanmış
              </Text>
              <Group justify="space-between" align="flex-end" gap={0}>
                <Text fw={700} size="sm">
                  {dashboardData.analysisCompletedCount.toLocaleString('tr-TR')} {label}
                </Text>
                <Text c="teal" fw={700} size="xs" className={classes.statCount} style={{ marginTop: 'auto' }}>
                  {((dashboardData.analysisCompletedCount / dashboardData.totalCount) * 100).toFixed(2)}%
                </Text>
              </Group>
            </Box>
            <Box
              style={{
                borderBottomColor: 'red',
                display: 'flex',
                flexDirection: 'column',
                cursor: 'pointer',
              }}
              className={classes.stat}
              onClick={() => {
                setFilter('false', false, setIsAnalysisCompletedFilterValue, 'isAnalysisCompleted');
              }}
            >
              <Text tt="uppercase" fz="xs" c="dimmed" fw={700}>
                Analizi Tamamlanmamış
              </Text>
              <Group justify="space-between" align="flex-end" gap={0}>
                <Text fw={700} size="sm">
                  {dashboardData.analysisUnCompletedCount.toLocaleString('tr-TR')} {label}
                </Text>
                <Text c="red" fw={700} size="xs" className={classes.statCount} style={{ marginTop: 'auto' }}>
                  {((dashboardData.analysisUnCompletedCount / dashboardData.totalCount) * 100).toFixed(2)}%
                </Text>
              </Group>
            </Box>
          </SimpleGrid>
        </HoverCard.Dropdown>
      </HoverCard>
    </Card>
  );
};

export default CallNumber;
