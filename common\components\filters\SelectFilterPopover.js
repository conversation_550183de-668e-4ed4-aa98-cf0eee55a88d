'use client';
import React, { useState } from 'react';
import { ActionI<PERSON>, Button, Popover, Stack } from '@mantine/core';
import { IconX } from '@tabler/icons-react';
import CustomListInput from './CustomListInput';

const SelectFilterPopover = function ({ label, value, onChange, data, multiple, icon }) {
  const [opened, setOpened] = useState(false);
  const [tempValue, setTempValue] = useState(value ? value : multiple ? [] : null);

  const isEmptyValue = (val) => {
    return multiple ? val.length === 0 : val === null;
  };

  const handleSelectAll = () => {
    const allValues = data.map((item) => item.value);
    setTempValue(allValues);
  };

  const handleDeselectAll = () => {
    setTempValue([]);
  };

  const isAllSelected = multiple && tempValue.length === data.length;

  return (
    <div style={{ display: 'flex', alignItems: 'center' }}>
      <Popover opened={opened} position="bottom" withArrow>
        <Popover.Target>
          <div>
            <Button
              leftSection={icon}
              size="xs"
              variant="light"
              radius="xl"
              color={isEmptyValue(value) ? 'black' : 'teal'}
              onClick={() => {
                if (opened) {
                  setOpened(false);
                } else {
                  setOpened(true);
                  setTempValue(value);
                }
              }}
            >
              {isEmptyValue(value)
                ? label
                : multiple
                ? label + '(' + value.length + ')'
                : data.find((x) => x.value === value).label}
            </Button>
          </div>
        </Popover.Target>
        <Popover.Dropdown>
          <div style={{ display: 'flex', width: '100%' }}>
            <ActionIcon
              style={{ marginLeft: 'auto' }}
              variant="subtle"
              color="black"
              onClick={() => {
                setOpened(false);
                setTempValue(value);
              }}
            >
              <IconX />
            </ActionIcon>
          </div>
          <Stack spacing="sm">
            {multiple && data.length > 0 && (
              <div style={{ display: 'flex', gap: '8px' }}>
                <Button
                  size="xs"
                  variant="light"
                  onClick={isAllSelected ? handleDeselectAll : handleSelectAll}
                >
                  {isAllSelected ? 'Tümünün Seçimini Kaldır' : 'Tümünü Seç'}
                </Button>
              </div>
            )}
            <CustomListInput
              value={tempValue}
              data={data}
              multiple={multiple}
              onChange={(val) => {
                setTempValue(val);
              }}
            />
            <Button
              onClick={() => {
                onChange(tempValue, isEmptyValue(tempValue));
                setOpened(false);
              }}
              size="xs"
              mt="sm"
            >
              Filtrele
            </Button>
          </Stack>
        </Popover.Dropdown>
      </Popover>
      {isEmptyValue(value) === false && (
        <ActionIcon
          variant="subtle"
          onClick={() => {
            setTempValue(multiple ? [] : null);
            onChange(multiple ? [] : null, true);
            setOpened(false);
          }}
        >
          <IconX />
        </ActionIcon>
      )}
    </div>
  );
};

export default SelectFilterPopover;
