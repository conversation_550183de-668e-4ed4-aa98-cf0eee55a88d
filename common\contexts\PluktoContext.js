'use client';
import React, { createContext, useState, useEffect, useContext, useCallback } from 'react';
import PropTypes from 'prop-types';
import { AuthContext } from './AuthContext';
import LoadingSpinner from '@/common/components/LoadingSpinner';

export const PluktoContext = createContext();

export const PluktoProvider = ({ children }) => {
  const { fetchAuthClient } = useContext(AuthContext);

  const [callTenantParameters, setCallTenantParameters] = useState(null);
  const [chatTenantParameters, setChatTenantParameters] = useState(null);

  const [callAgents, setCallAgents] = useState([]);
  const [chatAgents, setChatAgents] = useState([]);

  const [isLoading, setIsLoading] = useState(true);

  const [error, setError] = useState(null);

  const fetchTenantParameters = useCallback(
    async (type) => {
      if (!type || !fetchAuthClient) return null;

      try {
        const response = await fetchAuthClient(`Tenant/parameters/${type}`, {
          method: 'GET',
        });

        const data = await response.json();

        if (type === 'Call') {
          setCallTenantParameters(data);
        } else if (type === 'Chat') {
          setChatTenantParameters(data);
        }

        return data;
      } catch (error) {
        console.error(`Error fetching tenant parameters for ${type}:`, error);
        throw error;
      }
    },
    [fetchAuthClient]
  );

  const fetchAgents = useCallback(
    async (type) => {
      if (!type || !fetchAuthClient) return [];

      try {
        const response = await fetchAuthClient(`Agent/${type}`, {
          method: 'GET',
        });

        const data = await response.json();

        if (type === 'Call') {
          setCallAgents(data);
        } else if (type === 'Chat') {
          setChatAgents(data);
        }

        return data;
      } catch (error) {
        console.error(`Error fetching agents for ${type}:`, error);
        throw error;
      }
    },
    [fetchAuthClient]
  );

  const findCallAgent = useCallback(
    (agentId) => {
      return callAgents.find((agent) => agent.id === agentId || agent.id === parseInt(agentId));
    },
    [callAgents]
  );

  const findChatAgent = useCallback(
    (agentId) => {
      return chatAgents.find((agent) => agent.id === agentId || agent.id === parseInt(agentId));
    },
    [chatAgents]
  );

  const findAgent = useCallback(
    (agentId, type) => {
      if (type === 'Call') {
        return findCallAgent(agentId);
      } else if (type === 'Chat') {
        return findChatAgent(agentId);
      }

      return findCallAgent(agentId) || findChatAgent(agentId);
    },
    [findCallAgent, findChatAgent]
  );

  const getAgentName = useCallback(
    (agentId, type) => {
      const agent = findAgent(agentId, type);
      return agent?.name || agent?.fullName || `Agent ${agentId}`;
    },
    [findAgent]
  );

  useEffect(() => {
    const initializePluktoData = async () => {
      if (!fetchAuthClient) return;

      const publicPaths = ['/login', '/logout', '/lcw-kampanya', '/maintenance'];
      const currentPath = window.location.pathname;

      if (publicPaths.includes(currentPath)) {
        setIsLoading(false);
        return;
      }

      const accessToken = localStorage.getItem('accessToken');
      const refreshToken = localStorage.getItem('refreshToken');

      if (!accessToken || !refreshToken) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        await Promise.all([
          fetchTenantParameters('Call'),
          fetchTenantParameters('Chat'),
          fetchAgents('Call'),
          fetchAgents('Chat'),
        ]);
      } catch (error) {
        console.error('Failed to initialize Plukto data:', error);
        // Token ile ilgili hatalar için login'e yönlendir
        if (error.message && error.message.includes('Token yenileme işlemi başarısız')) {
          localStorage.removeItem('accessToken');
          localStorage.removeItem('refreshToken');
          window.location.href = '/login';
          return;
        }
        setError(error.message);
      } finally {
        setIsLoading(false);
      }
    };

    initializePluktoData();
  }, [fetchAuthClient, fetchTenantParameters, fetchAgents]);

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (error) {
    return (
      <div style={{ padding: '20px', textAlign: 'center' }}>
        <h3>Veri yüklenirken hata oluştu</h3>
        <p>{error}</p>
        <button onClick={() => window.location.reload()}>Sayfayı Yenile</button>
      </div>
    );
  }

  const contextValue = {
    callTenantParameters,
    chatTenantParameters,

    callAgents,
    chatAgents,

    findAgent,
    findCallAgent,
    findChatAgent,
    getAgentName,

    getTenantParameters: (channelType) => (channelType === 'Call' ? callTenantParameters : chatTenantParameters),
    getAgents: (channelType) => (channelType === 'Call' ? callAgents : chatAgents),
  };

  return <PluktoContext.Provider value={contextValue}>{children}</PluktoContext.Provider>;
};

PluktoProvider.propTypes = {
  children: PropTypes.node.isRequired,
};

export function usePlukto() {
  const context = useContext(PluktoContext);
  if (!context) {
    throw new Error('usePlukto must be used within a PluktoProvider');
  }
  return context;
}
