'use client';
import React from 'react';
import { AuthContext } from '@/common/contexts/AuthContext';
import { PluktoContext } from '@/common/contexts/PluktoContext';
import { Accordion, Avatar, Badge, Card, Loader, rem } from '@mantine/core';
import { spotlight, Spotlight } from '@mantine/spotlight';
import { IconArrowRight, IconMessage, IconPhone, IconSearch, IconUser } from '@tabler/icons-react';
import { useContext, useState } from 'react';
import { useDebouncedCallback } from '@mantine/hooks';
import { modals } from '@mantine/modals';
import { ChannelDetailModal } from '@/app/v2.0/channels/(channelDetail)/ChannelDetailModal';
import { FixedSizeList as List } from 'react-window';

export function PluktoSearch() {
  const { fetchAuthClient, permissions } = useContext(AuthContext);
  const { callTenantParameters, chatTenantParameters, callAgents, chatAgents } = useContext(PluktoContext);
  const [loading, setLoading] = useState(false);
  const [actions, setActions] = useState([]);
  const [search, setSearch] = useState('');

  const handleSearch = useDebouncedCallback(async (query) => {
    setLoading(true);
    setActions([]);
    try {
      if (query) {
        const response = await fetchAuthClient('Tenant/spoilerSearch/' + query);
        if (!response.ok) {
          throw new Error(`Network response was not ok: ${response.status}`);
        }
        const data = await response.json();
        const newActions = [];
        const callAgentActions = data.filter((x) => x.channelType === 'Call' && x.type === 'Agent');
        const chatAgentActions = data.filter((x) => x.channelType === 'Chat' && x.type === 'Agent');
        const callActions = data.filter((x) => x.channelType === 'Call' && x.type === 'Call');
        const chatActions = data.filter((x) => x.channelType === 'Chat' && x.type === 'Chat');
        if (callAgentActions.length > 0) {
          newActions.push({
            group: 'Çağrı Temsilcileri',
            count: callAgentActions.length,
            leftSection: (
              <>
                <IconPhone style={{ width: rem(24), height: rem(24) }} />
                <IconUser style={{ width: rem(24), height: rem(24) }} />
              </>
            ),
            actions: callAgentActions.map((x) => ({
              id: `call-agent-${x.id}`,
              label: x.nameSurname,
              description: x.email,
              leftSection: (
                <Avatar color="blue" radius="xl" style={{ marginRight: '8px' }}>
                  {x.nameSurname.split(' ').map((y) => y.charAt(0))}
                </Avatar>
              ),
              onClick: () => {
                spotlight.close();
                window.location.href = '/' + process.env.VERSION + '/channels?channelType=Call&agentId=' + x.id;
              },
            })),
          });
        }
        if (chatAgentActions.length > 0) {
          newActions.push({
            group: 'Yazışma Temsilcileri',
            count: chatAgentActions.length,
            leftSection: (
              <>
                <IconMessage style={{ width: rem(24), height: rem(24) }} />
                <IconUser style={{ width: rem(24), height: rem(24) }} />
              </>
            ),
            actions: chatAgentActions.map((x) => ({
              id: `chat-agent-${x.id}`,
              label: x.nameSurname,
              description: x.email,
              leftSection: (
                <Avatar color="teal" radius="xl" style={{ marginRight: '8px' }}>
                  {x.nameSurname.split(' ').map((y) => y.charAt(0))}
                </Avatar>
              ),
              onClick: () => {
                spotlight.close();
                window.location.href = '/' + process.env.VERSION + '/channels?channelType=Chat&agentId=' + x.id;
              },
            })),
          });
        }
        if (callActions.length > 0) {
          newActions.push({
            group: 'Çağrılar',
            count: callActions.length,
            leftSection: <IconPhone style={{ width: rem(24), height: rem(24) }} />,
            actions: callActions.map((x) => ({
              id: `call-call-${x.id}`,
              label: x.identifier,
              description: '',
              leftSection: <IconPhone style={{ width: rem(24), height: rem(24), marginRight: '8px' }} />,
              onClick: () => {
                spotlight.close();
                modals.open({
                  title: 'Çağrı Detayı',
                  size: '100%',
                  overlayProps: {
                    backgroundOpacity: 0.55,
                    blur: 3,
                  },
                  radius: 'md',
                  withCloseButton: true,
                  children: (
                    <ChannelDetailModal
                      channelType="Call"
                      id={x.id}
                      tenantParameters={callTenantParameters}
                      agents={callAgents}
                      fetchAuthClient={fetchAuthClient}
                      permissions={permissions}
                    />
                  ),
                });
              },
            })),
          });
        }
        if (chatActions.length > 0) {
          newActions.push({
            group: 'Yazışmalar',
            count: chatActions.length,
            leftSection: <IconMessage style={{ width: rem(24), height: rem(24) }} />,
            actions: chatActions.map((x) => ({
              id: `chat-chat-${x.id}`,
              label: x.identifier,
              description: '',
              leftSection: <IconMessage style={{ width: rem(24), height: rem(24), marginRight: '8px' }} />,
              onClick: () => {
                spotlight.close();
                modals.open({
                  title: 'Yazışma Detayı',
                  size: '100%',
                  overlayProps: {
                    backgroundOpacity: 0.55,
                    blur: 3,
                  },
                  radius: 'md',
                  withCloseButton: true,
                  children: (
                    <ChannelDetailModal
                      channelType="Chat"
                      id={x.id}
                      tenantParameters={chatTenantParameters}
                      agents={chatAgents}
                      fetchAuthClient={fetchAuthClient}
                      permissions={permissions}
                    />
                  ),
                });
              },
            })),
          });
        }
        setActions(newActions);
      }
    } catch (error) {
      console.error('Search error:', error);
    } finally {
      setLoading(false);
    }
  }, 500);


  const renderRow =
    (items) =>
    ({ index, style }) => {
      const subAction = items[index];
      return (
        <div
          key={index}
          className="cursor-pointer"
          onClick={() => subAction.onClick()}
          style={{
            ...style,
            display: 'flex',
            flexDirection: 'column',
            marginLeft: '20px',
            marginTop: '8px',
            paddingRight: '20px',
          }}
        >
          <Card shadow="sm" padding="sm" radius="md" withBorder style={{ borderLeft: '4px solid gray' }}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              {subAction.leftSection}
              <span>
                <b>{subAction.label}</b>
                {subAction.description && (
                  <>
                    <br />
                    <small>{subAction.description}</small>
                  </>
                )}
              </span>
              <IconArrowRight style={{ width: rem(24), height: rem(24), marginLeft: 'auto' }} />
            </div>
          </Card>
        </div>
      );
    };

  return (
    <Spotlight.Root highlightQuery maxHeight={600} scrollable>
      <Spotlight.Search
        value={search}
        onChange={(event) => {
          setSearch(event.currentTarget.value);
          handleSearch(event.currentTarget.value);
        }}
        placeholder="Çağrı veya yazışma id yada temsilci ismi ile arat..."
        leftSection={<IconSearch style={{ width: rem(20), height: rem(20) }} stroke={1.5} />}
      />
      <Spotlight.ActionsList>
        {loading ? (
          <Spotlight.Empty>
            <Loader size="sm" />
          </Spotlight.Empty>
        ) : actions.length === 0 ? (
          <Spotlight.Empty>Arama sonucunda, bir eşleşme bulunamadı.</Spotlight.Empty>
        ) : (
          <Accordion>
            {actions.map((action, index) => (
              <Accordion.Item key={index} value={action.group}>
                <Accordion.Control icon={action.leftSection}>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <span>{action.group}</span>
                    <Badge size="sm" ml="xs" color="gray">
                      {action.count}
                    </Badge>
                  </div>
                </Accordion.Control>
                <Accordion.Panel>
                  <List
                    height={Math.min(action.actions.length * 80, 400)}
                    itemCount={action.actions.length}
                    itemSize={80}
                    width="100%"
                    style={{ marginTop: '8px' }}
                  >
                    {renderRow(action.actions)}
                  </List>
                </Accordion.Panel>
              </Accordion.Item>
            ))}
          </Accordion>
        )}
      </Spotlight.ActionsList>
    </Spotlight.Root>
  );
}
