'use client';

import React from 'react';
import { AuthContext } from '@/common/contexts/AuthContext';
import { useContext } from 'react';
import { useForm } from '@mantine/form';
import { handleFormPostSubmit, convertToFormData } from '@/common/functions/formFunctions';
import { FormError } from '@/common/components/FormError';
import Link from 'next/link';

export default function Login() {
  const { user, permissions } = useContext(AuthContext);
  const form = useForm({
    mode: 'controlled',
    initialValues: { email: '', password: '' },
  });

  if (user && permissions) {
    if (permissions.includes('Call.View')) {
      window.location.href = '/' + process.env.VERSION + '/calls';
    } else if (permissions?.includes('Chat.View')) {
      window.location.href = '/' + process.env.VERSION + '/chats';
    } else {
      window.location.href = '/' + process.env.VERSION;
    }
    return null;
  }

  const handleSubmit = async (e) => {
    e.preventDefault();
    handleFormPostSubmit(
      e,
      form,
      fetch(process.env.API_URL + 'Auth/login', {
        method: 'POST',
        headers: {
          'x-api-version': process.env.VERSION,
        },
        body: convertToFormData(form.getValues()),
      }),
      async (response) => {
        const data = await response.json();
        localStorage.setItem('accessToken', data.accessToken);
        localStorage.setItem('refreshToken', data.refreshToken);
        window.location.href = '/' + process.env.VERSION;
      }
    );
  };

  return (
    <div className="min-h-screen w-full flex items-center justify-center bg-gradient-to-br from-green-50 via-white to-green-100 relative overflow-hidden py-10">
      <div className="absolute top-0 left-0 w-[500px] h-[500px] bg-green-300 opacity-20 rounded-full blur-3xl -z-10 animate-pulse"></div>
      <div className="absolute bottom-0 right-0 w-[500px] h-[500px] bg-green-200 opacity-20 rounded-full blur-3xl -z-10 animate-pulse"></div>
      <div className="absolute top-1/4 right-1/4 w-64 h-64 bg-green-400 opacity-10 rounded-full blur-3xl -z-10 animate-pulse delay-700"></div>
      <div className="absolute bottom-1/3 left-1/3 w-96 h-96 bg-green-100 opacity-30 rounded-full blur-3xl -z-10 animate-pulse delay-1000"></div>

      <div className="absolute top-10 left-10 w-6 h-6 border-t-4 border-l-4 border-green-400 opacity-60"></div>
      <div className="absolute bottom-10 right-10 w-6 h-6 border-b-4 border-r-4 border-green-400 opacity-60"></div>
      <div className="absolute top-10 right-10 w-4 h-4 bg-green-400 rounded-full opacity-60"></div>
      <div className="absolute bottom-10 left-10 w-4 h-4 bg-green-400 rounded-full opacity-60"></div>

      <div className="max-w-5xl w-full mx-4 md:mx-auto flex flex-col md:flex-row rounded-3xl shadow-[0_20px_50px_rgba(8,107,46,0.15)] bg-white/95 backdrop-blur-xl border border-green-100/50 overflow-hidden max-h-[90vh] md:max-h-[80vh]">
        {/* Left panel */}
        <div className="hidden md:flex flex-col justify-center relative items-center w-1/2 bg-gradient-to-br from-green-500 via-green-600 to-green-700 p-8 overflow-y-hidden">
          <div className="absolute inset-0 bg-[url('/pattern.svg')] opacity-10"></div>
          <div className="absolute inset-0 bg-gradient-to-br from-green-400/20 to-transparent"></div>

          <div className="relative z-10 flex flex-col items-center">
            <div className="bg-white/95 rounded-2xl p-4 shadow-xl mb-6 transform hover:scale-105 transition-transform duration-300">
              <img src="/logo.svg" alt="Plukto Logo" className="w-40 h-24 drop-shadow-xl" />
            </div>

            <h1 className="text-4xl font-extrabold text-white mb-4 text-center drop-shadow-lg">
              Plukto&apos;ya <span className="text-green-200">Hoş Geldiniz</span>
            </h1>

            <p className="text-white text-lg text-center font-medium mb-6 leading-relaxed">
              Yapay zeka destekli çağrı analiz platformu ile müşteri görüşmelerinizi{' '}
              <span className="text-green-200 font-bold">anlamlandırın</span>,{' '}
              <span className="text-green-200 font-bold">kategorize edin</span> ve{' '}
              <span className="text-green-200 font-bold">iyileştirin</span>.
            </p>

            <div className="bg-white/10 backdrop-blur-sm rounded-2xl py-5 px-4 border border-white/20 shadow-lg">
              <ul className="text-white text-base space-y-3 -ml-5  ">
                <li className="flex items-center">
                  <span className="mr-3 text-green-200 text-xl">✓</span>
                  <span>Çağrı kayıtlarını metne dökün ve analiz edin</span>
                </li>
                <li className="flex items-center">
                  <span className="mr-3 text-green-200 text-xl">✓</span>
                  <span>Kriterlerinize göre otomatik değerlendirme</span>
                </li>
                <li className="flex items-center">
                  <span className="mr-3 text-green-200 text-xl">✓</span>
                  <span>Kök neden analizi ile sorunları keşfedin</span>
                </li>
                <li className="flex items-center">
                  <span className="mr-3 text-green-200 text-xl">✓</span>
                  <span>Tüm görüşmeleri raporlayın</span>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Right panel  */}
        <div className="w-full md:w-1/2 flex flex-col items-center justify-center px-6 py-8 bg-white/95 backdrop-blur-xl overflow-y-auto">
          <div className="md:hidden flex flex-col items-center mb-6">
            <div className="bg-gradient-to-r from-green-500 to-green-600 p-2 rounded-xl shadow-lg mb-4">
              <img src="/logo.svg" alt="Plukto Logo" className="w-20 h-20 drop-shadow-xl bg-white p-2 rounded-lg" />
            </div>
            <h1 className="text-2xl font-extrabold text-green-600 mb-2 text-center drop-shadow-lg">
              Plukto&apos;ya Hoş Geldiniz
            </h1>
          </div>

          <h2 className="text-2xl font-bold text-gray-800 mb-6 hidden md:block">Hesabınıza Giriş Yapın</h2>

          <form className="w-full max-w-md space-y-5" onSubmit={handleSubmit}>
            <div className="group">
              <label
                className="block text-gray-700 font-semibold mb-1 text-base group-focus-within:text-green-600 transition-colors duration-200"
                htmlFor="email"
              >
                E-Posta
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <svg
                    className="h-5 w-5 text-gray-400 group-focus-within:text-green-500"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"
                    />
                  </svg>
                </div>
                <input
                  id="email"
                  type="email"
                  autoComplete="email"
                  {...form.getInputProps('email')}
                  className="w-full pl-12 pr-4 py-3 rounded-xl bg-white text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 font-medium shadow-sm border border-gray-200 hover:border-green-200 transition-all duration-200"
                  placeholder="E-posta adresinizi girin"
                  required
                />
              </div>
            </div>

            <div className="group">
              <label
                className="block text-gray-700 font-semibold mb-1 text-base group-focus-within:text-green-600 transition-colors duration-200"
                htmlFor="password"
              >
                Şifre
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <svg
                    className="h-5 w-5 text-gray-400 group-focus-within:text-green-500"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                    />
                  </svg>
                </div>
                <input
                  id="password"
                  type="password"
                  autoComplete="current-password"
                  {...form.getInputProps('password')}
                  className="w-full pl-12 pr-4 py-3 rounded-xl bg-white text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 font-medium shadow-sm border border-gray-200 hover:border-green-200 transition-all duration-200"
                  placeholder="Şifrenizi girin"
                  required
                />
              </div>
              <div className="flex justify-end mt-1">
                <Link href="/forgot-password" className="text-green-600 hover:text-green-700 text-sm font-medium">
                  Şifremi unuttum
                </Link>
              </div>
            </div>

            <button
              type="submit"
              className="w-full py-3 rounded-xl bg-gradient-to-r from-green-500 to-green-600 text-white font-bold text-lg shadow-lg hover:shadow-green-200/50 hover:brightness-105 hover:scale-[1.02] active:scale-[0.98] transition-all duration-200"
            >
              Giriş Yap
            </button>

            <FormError errorText={form.errors['formError']} />
          </form>

          <div className="mt-6 text-sm text-gray-500 text-center">
            © {new Date().getFullYear()} Plukto. Tüm hakları saklıdır.
          </div>
        </div>
      </div>
    </div>
  );
}
