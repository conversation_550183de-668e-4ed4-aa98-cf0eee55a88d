import React, { useCallback, useMemo, useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { ReactFlow, useNodesState, useEdgesState, addEdge, Position, Handle, NodeToolbar } from '@xyflow/react';
import { Group, Button, Text } from '@mantine/core';
import '@xyflow/react/dist/style.css';

const SubCategoryNode = ({ data, selected }) => {
  const isTextTruncated = data.originalName && data.originalName.length > 15;

  return (
    <>
      {isTextTruncated && (
        <NodeToolbar
          isVisible={selected}
          position={Position.Top}
          offset={10}
          align="center"
        >
          <div
            style={{
              background: 'rgba(0, 0, 0, 0.8)',
              color: 'white',
              padding: '6px 12px',
              borderRadius: '6px',
              fontSize: '12px',
              maxWidth: '250px',
              wordBreak: 'break-word',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
            }}
          >
            {data.originalName}
          </div>
        </NodeToolbar>
      )}

      <div
        style={{
          padding: '4px 8px',
          background: data.activeTab === 'combined' ? '#f0f9ff' : '#e3f2fd',
          border: `1px solid ${data.activeTab === 'combined' ? '#67e8f9' : '#90caf9'}`,
          borderRadius: '12px',
          fontWeight: '400',
          fontSize: '10px',
          color: data.activeTab === 'combined' ? '#0891b2' : '#1976d2',
          display: 'flex',
          alignItems: 'center',
          gap: '4px',
          minWidth: '60px',
          justifyContent: 'center',
          maxWidth: '150px',
          width: '150px',
          whiteSpace: 'nowrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
        }}
      >
        <span style={{ overflow: 'hidden', textOverflow: 'ellipsis' }}>
          {data.displayName}
        </span>
        <span
          style={{
            background: data.activeTab === 'combined' ? 'rgba(8,145,178,0.1)' : 'rgba(25,118,210,0.1)',
            padding: '1px 4px',
            borderRadius: '6px',
            fontSize: '9px',
            flexShrink: 0,
          }}
        >
          {data.count}
        </span>
      </div>

      <Handle type="target" position={Position.Left} />
      <Handle type="source" position={Position.Right} />
    </>
  );
};

const KeywordFlowChart = ({ keywordData, onNavigate, wordDetails }) => {
  const [activeTab, setActiveTab] = useState('call');
  const nodeTypes = useMemo(() => ({
    subCategory: SubCategoryNode,
  }), []);

  const availableTabs = useMemo(() => {
    const tabs = [];
    if (keywordData?.call && keywordData.call.length > 0) {
      tabs.push({ key: 'call', label: 'Çağrı', color: '#228be6' });
    }
    if (keywordData?.chat && keywordData.chat.length > 0) {
      tabs.push({ key: 'chat', label: 'Yazışma', color: '#fd7e14' });
    }
    if (keywordData?.combined && keywordData.combined.length > 0) {
      tabs.push({ key: 'combined', label: 'Toplam', color: '#37b24d' });
    }
    return tabs;
  }, [keywordData]);

  useEffect(() => {
    if (availableTabs.length > 0 && !availableTabs.find((tab) => tab.key === activeTab)) {
      setActiveTab(availableTabs[0].key);
    }
  }, [availableTabs, activeTab]);

  const { nodes: initialNodes, edges: initialEdges } = useMemo(() => {
    const currentData = keywordData?.[activeTab];
    if (!currentData || !currentData.length) {
      return { nodes: [], edges: [] };
    }

    const nodes = [];
    const edges = [];
    let nodeId = 0;

    const rootKeyword = currentData[0];
    const rootNodeId = `node-${nodeId++}`;
    const tabConfig = availableTabs.find((tab) => tab.key === activeTab);
    const channelColor = tabConfig?.color || '#228be6';

    nodes.push({
      id: rootNodeId,
      type: 'default',
      position: { x: 50, y: 150 },
      sourcePosition: Position.Right,
      targetPosition: Position.Right,
      data: {
        label: (
          <div
            style={{
              padding: '8px 16px',
              background: '#1976d2',
              color: 'white',
              borderRadius: '20px',
              fontWeight: '600',
              fontSize: '14px',
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              minWidth: '140px',
              justifyContent: 'center',
            }}
          >
            <span>{rootKeyword.name}</span>
            <span
              style={{
                background: 'rgba(255,255,255,0.2)',
                padding: '2px 8px',
                borderRadius: '12px',
                fontSize: '12px',
              }}
            >
              {rootKeyword.count.toLocaleString('tr-TR')}
            </span>
          </div>
        ),
      },
      style: {
        background: 'transparent',
        border: 'none',
        width: 'auto',
        height: 'auto',
      },
    });

    const nextNodeX = 300;
    const sourceNodeId = rootNodeId;

    if (rootKeyword.subTitles && rootKeyword.subTitles.length > 0) {
      const categoryStartX = nextNodeX;
      const sortedCategories = [...rootKeyword.subTitles].sort((a, b) => b.count - a.count);
      const maxCategoriesToShow = 3;
      const categoriesToShow = sortedCategories.slice(0, maxCategoriesToShow);

      const totalCategories = categoriesToShow.length;
      const categorySpacing = Math.max(150, 300 / totalCategories);

      categoriesToShow.forEach((category, index) => {
        const categoryNodeId = `category-${nodeId++}`;
        const categoryY = 50 + index * categorySpacing;

        nodes.push({
          id: categoryNodeId,
          type: 'default',
          position: { x: categoryStartX, y: categoryY },
          sourcePosition: Position.Right,
          targetPosition: Position.Left,
          data: {
            label: (
              <div
                style={{
                  padding: '5px 10px',
                  background: activeTab === 'combined' ? '#e8f5e8' : '#f8f9fa',
                  border: `1px solid ${activeTab === 'combined' ? '#51cf66' : '#dee2e6'}`,
                  borderRadius: '14px',
                  fontWeight: '500',
                  fontSize: '12px',
                  color: activeTab === 'combined' ? '#37b24d' : '#495057',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '6px',
                  minWidth: '80px',
                  justifyContent: 'center',
                }}
              >
                <span>{category.name}</span>
                <span
                  style={{
                    background: activeTab === 'combined' ? '#d3f9d8' : '#e9ecef',
                    padding: '1px 5px',
                    borderRadius: '8px',
                    fontSize: '10px',
                    color: activeTab === 'combined' ? '#2b8a3e' : '#6c757d',
                  }}
                >
                  {category.count.toLocaleString('tr-TR')}
                </span>
              </div>
            ),
          },
          style: {
            background: 'transparent',
            border: 'none',
          },
        });

        edges.push({
          id: `${sourceNodeId}-${categoryNodeId}`,
          source: sourceNodeId,
          target: categoryNodeId,
          type: 'smoothstep',
          style: { stroke: activeTab === 'combined' ? '#37b24d' : '#666', strokeWidth: 1.5 },
        });

        if (category.subTitles && category.subTitles.length > 0) {
          const sortedSubCategories = [...category.subTitles].sort((a, b) => b.count - a.count);
          const maxSubCategoriesToShow = 3;
          const subCategoriesToShow = sortedSubCategories.slice(0, maxSubCategoriesToShow);

          const subCategoryBaseX = 600;
          const subCategoryYSpacing = 50;

          subCategoriesToShow.forEach((subCategory, subIndex) => {
            const subCategoryNodeId = `subcategory-${nodeId++}`;
            const subCategoryY = categoryY - 25 + subIndex * subCategoryYSpacing;

            nodes.push({
              id: subCategoryNodeId,
              type: 'subCategory',
              position: {
                x: subCategoryBaseX,
                y: subCategoryY,
              },
              targetPosition: Position.Left,
              sourcePosition: Position.Left,
              data: {
                originalName: subCategory.name,
                displayName: subCategory.name.length > 15 ? subCategory.name.substring(0, 15) + '...' : subCategory.name,
                count: subCategory.count.toLocaleString('tr-TR'),
                activeTab: activeTab,
              },
              style: {
                background: 'transparent',
                border: 'none',
              },
            });

            edges.push({
              id: `${categoryNodeId}-${subCategoryNodeId}`,
              source: categoryNodeId,
              target: subCategoryNodeId,
              type: 'smoothstep',
              style: { stroke: activeTab === 'combined' ? '#67e8f9' : '#999', strokeWidth: 1 },
            });
          });
        }
      });
    }

    return { nodes, edges };
  }, [keywordData, activeTab, onNavigate, wordDetails, availableTabs]);

  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);

  useEffect(() => {
    setNodes(initialNodes);
    setEdges(initialEdges);
  }, [initialNodes, initialEdges, setNodes, setEdges]);

  const onConnect = useCallback((params) => setEdges((eds) => addEdge(params, eds)), [setEdges]);

  if (!keywordData || (!keywordData.call && !keywordData.chat && !keywordData.combined) || availableTabs.length === 0) {
    return (
      <div
        style={{
          width: '100%',
          height: '300px',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#666',
          fontSize: '14px',
          padding: '20px',
          textAlign: 'center',
        }}
      >
        <div style={{ fontSize: '16px', fontWeight: '500', marginBottom: '8px' }}>📊 {wordDetails?.text}</div>
        <div style={{ fontSize: '13px', color: '#888', marginBottom: '12px' }}>
          Bu anahtar kelime için detaylı kategori verisi bulunmuyor
        </div>
        <div style={{ fontSize: '12px', color: '#999' }}>
          Çağrı: {wordDetails?.callCount || 0} • Yazışma: {wordDetails?.chatCount || 0}
        </div>
      </div>
    );
  }

  return (
    <div style={{ width: '950px', height: '380px' }}>
      {/* Tab Header */}
      {availableTabs.length > 1 && (
        <div style={{ marginBottom: '12px', paddingLeft: '8px' }}>
          <Group spacing="xs">
            <Text size="sm" weight={500} color="dark">
              📊 {wordDetails?.text}
            </Text>
            <Text size="xs" color="dimmed">
              • Toplam: {wordDetails?.totalCount || 0}
            </Text>
          </Group>
          <Group spacing="xs" mt="xs">
            {availableTabs.map((tab) => (
              <Button
                key={tab.key}
                size="xs"
                variant={activeTab === tab.key ? 'filled' : 'light'}
                color={activeTab === tab.key ? tab.color : 'gray'}
                onClick={() => setActiveTab(tab.key)}
                style={{ fontSize: '11px', height: '28px' }}
              >
                {tab.label} (
                {tab.key === 'call'
                  ? wordDetails?.callCount || 0
                  : tab.key === 'chat'
                  ? wordDetails?.chatCount || 0
                  : wordDetails?.totalCount || 0}
                )
              </Button>
            ))}
          </Group>
        </div>
      )}

      {/* Flow Chart */}
      <div style={{ height: availableTabs.length > 1 ? '400px' : '425px' }}>
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          nodeTypes={nodeTypes}
          fitView
          fitViewOptions={{ padding: 0.1 }}
          attributionPosition="bottom-left"
          proOptions={{ hideAttribution: true }}
          style={{ background: '#ffffff' }}
          nodesDraggable={false}
        />
      </div>
    </div>
  );
};

KeywordFlowChart.propTypes = {
  keywordData: PropTypes.shape({
    call: PropTypes.array,
    chat: PropTypes.array,
    combined: PropTypes.array,
  }),
  onNavigate: PropTypes.func,
  wordDetails: PropTypes.shape({
    text: PropTypes.string,
    callCount: PropTypes.number,
    chatCount: PropTypes.number,
    totalCount: PropTypes.number,
  }),
};

export default KeywordFlowChart;
