'use client';
import React from 'react';
import { AuthContext } from '@/common/contexts/AuthContext';
import { Card, Grid, Button, Group, Text } from '@mantine/core';
import { IconFileTypePdf, IconDownload } from '@tabler/icons-react';
import { useContext, useEffect, useState } from 'react';
import DashboardSkeleton from './_components/DashboardSkeleton';
import WordCloudComp from './_components/WordCloudComp';
import AlarmStatus from './_components/AlarmStatus';
import CustomerSatisfaction from './_components/CustomerSatisfaction';
import TopCallReasons from './_components/TopCallReasons';
import WhereWeMakingMistake from './_components/WhereWeMakingMistake';
import TopFiveComplaints from './_components/TopFiveComplaints';
import DateFilterPopover from '@/common/components/filters/DateFilterPopover';
import { getDatesFromType } from '@/common/functions/commonFunctions';
import { useRouter } from 'next/navigation';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';

export default function Home() {
  const { permissions, fetchAuthClient } = useContext(AuthContext);
  const date = getDatesFromType('lastonemonth');
  const [dateFilterValue, setDateFilterValue] = useState(date);
  const [callDashboardData, setCallDashboardData] = useState(null);
  const [chatDashboardData, setChatDashboardData] = useState(null);
  const [filters, setFilters] = useState([
    {
      id: 'date',
      value: date,
    },
    {
      id: 'isAnalysisCompleted',
      value: 'true',
    },
    {
      id: 'isAgentActive',
      value: 'true',
    },
  ]);
  const router = useRouter();

  const fetchDashboardDatas = async () => {
    setChatDashboardData(null);
    setCallDashboardData(null);
    const [chatResponse, callResponse] = await Promise.all([
      fetchAuthClient(`Chat/dashboard`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(filters),
      }),
      fetchAuthClient(`Call/dashboard`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(filters),
      }),
    ]);
    const [chatData, callData] = await Promise.all([chatResponse.json(), callResponse.json()]);
    setChatDashboardData(chatData);
    setCallDashboardData(callData);
  };

  const navigateToChannelsWithFilters = (channelType, additionalParams = []) => {
    const queryParams = new URLSearchParams();
    queryParams.append('channelType', channelType);
    const tempFilters = [...filters, ...additionalParams];
    const tempFiltersJsonString = JSON.stringify(tempFilters);
    const utf8Bytes = new TextEncoder().encode(tempFiltersJsonString);
    const base64String = btoa(String.fromCharCode(...utf8Bytes));
    queryParams.append('filters', base64String);
    router.push(`/${process.env.VERSION}/channels?${queryParams.toString()}`);
  };

  useEffect(() => {
    fetchDashboardDatas();
  }, [filters]);

  if (permissions.includes('Call.View') && permissions.includes('Chat.View')) {
  } else if (permissions.includes('Call.View')) {
    window.location.href = '/' + process.env.VERSION + '/channels?channelType=Call';
    return;
  } else if (permissions.includes('Chat.View')) {
    window.location.href = '/' + process.env.VERSION + '/channels?channelType=Chat';
    return;
  } else {
    window.location.href = '/401';
    return;
  }

  const handleExport = async () => {
 const element = document.getElementById('pdf-content');

    if (!element) return;

    // Sayfanın ekran görüntüsünü al
    const canvas = await html2canvas(element, {
      scrollY: -window.scrollY,
      useCORS: true
    });

    const imgData = canvas.toDataURL('image/png');

    // PDF boyutları
    const pdf = new jsPDF('p', 'mm', 'a4');
    const pdfWidth = pdf.internal.pageSize.getWidth();
    const pdfHeight = pdf.internal.pageSize.getHeight();

    // Görüntü oranlarını koruyarak uygun boyutları hesapla
    const imgProps = pdf.getImageProperties(imgData);
    const imgWidth = imgProps.width;
    const imgHeight = imgProps.height;

    const ratio = Math.min(pdfWidth / imgWidth, pdfHeight / imgHeight);
    const imgScaledWidth = imgWidth * ratio;
    const imgScaledHeight = imgHeight * ratio;

    // Ortalayarak resmi ekle
    const x = (pdfWidth - imgScaledWidth) / 2;
    const y = (pdfHeight - imgScaledHeight) / 2;

    pdf.addImage(imgData, 'PNG', x, y, imgScaledWidth, imgScaledHeight);
    pdf.save('tek_sayfa.pdf');
  };
  return (
    <div id="pdf-content">
      <Card shadow="sm" padding="lg" radius="md" withBorder style={{ position: 'sticky', top: 0, zIndex: 2 }}>
        <Group justify="space-between" align="center">
          <DateFilterPopover
            value={dateFilterValue}
            onChange={(value, isEmpty) => {
              setDateFilterValue(value);
              setFilters((prevFilters) => {
                const filtered = prevFilters.filter((filter) => filter.id !== 'date');
                if (isEmpty) {
                  return [...filtered];
                } else {
                  return [
                    ...filtered,
                    {
                      id: 'date',
                      value: value,
                    },
                  ];
                }
              });
            }}
            label={'Tarih Aralığı'}
            initDateTypeValue={'lastonemonth'}
          />
          <Button
            leftSection={<IconFileTypePdf size={18} />}
            variant="gradient"
            gradient={{ from: 'red', to: 'orange', deg: 45 }}
            onClick={handleExport}
            size="sm"
            radius="md"
            style={{
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
              transition: 'all 0.2s ease',
            }}
            styles={{
              root: {
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: '0 6px 20px rgba(0, 0, 0, 0.15)',
                },
              },
            }}
          >
            <Text size="sm" fw={500}>PDF Olarak Kaydet</Text>
          </Button>
        </Group>
      </Card>
      {!callDashboardData || !chatDashboardData ? (
        <DashboardSkeleton />
      ) : (
        <Grid mt="md">
          <Grid.Col span={{ md: 7 }}>
            <WordCloudComp
              dashboardDataCall={callDashboardData}
              dashboardDataChat={chatDashboardData}
              onItemClick={(item) => {
                navigateToChannelsWithFilters(item.type, [
                  {
                    id: 'keywords',
                    value: [item.keywords],
                  },
                ]);
              }}
            />
            <CustomerSatisfaction
              dashboardDataCall={callDashboardData}
              dashboardDataChat={chatDashboardData}
              filters={filters}
              onItemClick={(item) => {
                navigateToChannelsWithFilters(item.type, [
                  {
                    id: 'sentiment',
                    value: [item.sentiment],
                  },
                ]);
              }}
              style={{ marginTop: '1rem' }}
            />
          </Grid.Col>
          <Grid.Col span={{ md: 5 }}>
            <AlarmStatus
              dashboardDataCall={callDashboardData}
              dashboardDataChat={chatDashboardData}
              onItemClick={(item) => {
                navigateToChannelsWithFilters(item.type, [
                  {
                    id: 'alarmCategories',
                    value: [item.alarmCategory],
                  },
                ]);
              }}
            />
          </Grid.Col>
          <Grid.Col span={{ md: 12 }}>
            <Grid>
              <Grid.Col span={{ md: 6 }}>
                <TopCallReasons
                  dashboardDataCall={callDashboardData}
                  dashboardDataChat={chatDashboardData}
                  onItemClick={(item) => {
                    navigateToChannelsWithFilters(item.type, [
                      {
                        id: 'category',
                        value: [item.category],
                      },
                    ]);
                  }}
                />
              </Grid.Col>
              <Grid.Col span={{ md: 6 }}>
                <WhereWeMakingMistake
                  dashboardDataCall={callDashboardData}
                  dashboardDataChat={chatDashboardData}
                  onItemClick={(item) => {
                    navigateToChannelsWithFilters(item.type, [
                      {
                        id: 'analysis',
                        value: [item.analysis],
                      },
                    ]);
                  }}
                />
              </Grid.Col>
            </Grid>
          </Grid.Col>
          <Grid.Col span={{ md: 12 }}>
            <TopFiveComplaints
              dashboardDataCall={callDashboardData}
              dashboardDataChat={chatDashboardData}
              onItemClick={(item) => {
                navigateToChannelsWithFilters(item.type, [
                  {
                    id: 'subCategory',
                    value: [item.subCategory],
                  },
                ]);
              }}
            />
          </Grid.Col>
        </Grid>
      )}
    </div>
  );
}
