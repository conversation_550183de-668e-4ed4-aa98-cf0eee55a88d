import React from 'react';
import * as ExcelJS from 'exceljs';

/**
 * Günlük Temsilci Bazlı Analiz Raporu Template
 * Örnek format:
 * 12/06/2025				
 * Temsilci	Kanal	Firma	AI Analiz Adedi	AI Analiz Ortalama Skoru
 * BILGE OVALI	INBOUND	Ceyber	97	89.3
 * MUJDE OZSOY	INBOUND	Ceyber	69	81.3
 * ...
 */

export const generateDailyAgentAnalysisReport = async (
  dataToExport,
  agents,
  channelType,
  entityText
) => {
  try {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Günlük Temsilci Analizi');

    // Bugünün tarihini ekle
    const today = new Date();
    const dateStr = today.toLocaleDateString('tr-TR');
    
    // Tarih başlığı
    worksheet.getCell('A1').value = dateStr;
    worksheet.getCell('A1').font = { bold: true, size: 14 };
    worksheet.getCell('A1').alignment = { horizontal: 'left' };

    // Header row (3. satır)
    const headers = ['Temsilci', 'Kanal', 'Firma', 'AI Analiz Adedi', 'AI Analiz Ortalama Skoru'];
    const headerRow = worksheet.getRow(3);
    headers.forEach((header, index) => {
      headerRow.getCell(index + 1).value = header;
    });

    // Header styling
    headerRow.font = { bold: true, color: { argb: 'FFFFFF' } };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '4472C4' },
    };
    headerRow.alignment = { horizontal: 'center', vertical: 'middle' };
    headerRow.height = 25;

    // Temsilci bazlı veri toplama
    const agentStats = {};
    dataToExport.forEach((item) => {
      const agentId = item.agentId;
      const agent = agents.find((a) => a.id === agentId);
      const agentName = agent ? `${agent.name} ${agent.surname}` : `Agent ${agentId}`;
      const company = item.vendor || item.company || 'Bilinmeyen Şirket';
      
      // Kanal tipini belirle
      let channelName = 'UNKNOWN';
      if (channelType === 'Call') {
        channelName = 'INBOUND'; // veya item'dan alınabilir
      } else if (channelType === 'Chat') {
        channelName = 'CHAT';
      }

      if (!agentStats[agentId]) {
        agentStats[agentId] = {
          agentName,
          channelName,
          company,
          analysisCount: 0,
          totalScore: 0,
          validScoreCount: 0,
        };
      }

      agentStats[agentId].analysisCount++;
      
      // AI Analiz skoru (point field'ından)
      if (item.info && item.info.point !== undefined && item.info.point !== null && !isNaN(item.info.point)) {
        agentStats[agentId].totalScore += Number(item.info.point);
        agentStats[agentId].validScoreCount++;
      }
    });

    // Firma bazlı gruplama
    const companiesByName = {};
    Object.values(agentStats).forEach((stats) => {
      if (!companiesByName[stats.company]) {
        companiesByName[stats.company] = [];
      }
      companiesByName[stats.company].push(stats);
    });

    let currentRow = 4;

    // Firma bazlı veri yazma
    Object.entries(companiesByName)
      .sort(([a], [b]) => a.localeCompare(b, 'tr'))
      .forEach(([companyName, companyAgents]) => {
        // Temsilcileri alfabetik sırala
        companyAgents
          .sort((a, b) => a.agentName.localeCompare(b.agentName, 'tr'))
          .forEach((stats) => {
            const avgScore = stats.validScoreCount > 0 
              ? (stats.totalScore / stats.validScoreCount).toFixed(1) 
              : '0.0';

            const row = worksheet.getRow(currentRow);
            row.getCell(1).value = stats.agentName;
            row.getCell(2).value = stats.channelName;
            row.getCell(3).value = stats.company;
            row.getCell(4).value = stats.analysisCount;
            row.getCell(5).value = parseFloat(avgScore);

            // Satır styling
            row.height = 20;
            row.eachCell((cell) => {
              cell.border = {
                top: { style: 'thin', color: { argb: 'FFCCCCCC' } },
                bottom: { style: 'thin', color: { argb: 'FFCCCCCC' } },
                left: { style: 'thin', color: { argb: 'FFCCCCCC' } },
                right: { style: 'thin', color: { argb: 'FFCCCCCC' } },
              };
              cell.alignment = { horizontal: 'center', vertical: 'middle' };
            });

            // Sayısal değerler için locale formatting
            row.getCell(4).numFmt = '#,##0';
            row.getCell(5).numFmt = '0.0';

            currentRow++;
          });

        // Firma ortalaması hesapla
        const companyTotalAnalysis = companyAgents.reduce((sum, agent) => sum + agent.analysisCount, 0);
        const companyTotalScore = companyAgents.reduce((sum, agent) => sum + agent.totalScore, 0);
        const companyValidScoreCount = companyAgents.reduce((sum, agent) => sum + agent.validScoreCount, 0);
        const companyAvgScore = companyValidScoreCount > 0 
          ? (companyTotalScore / companyValidScoreCount).toFixed(1) 
          : '0.0';

        // Firma ortalaması satırı
        const companyRow = worksheet.getRow(currentRow);
        companyRow.getCell(1).value = '';
        companyRow.getCell(2).value = '';
        companyRow.getCell(3).value = `${companyName} Ort.`;
        companyRow.getCell(4).value = companyTotalAnalysis;
        companyRow.getCell(5).value = parseFloat(companyAvgScore);

        // Firma ortalaması styling
        companyRow.font = { bold: true, color: { argb: 'FF0066CC' } };
        companyRow.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFE6F3FF' },
        };
        companyRow.height = 22;
        companyRow.eachCell((cell) => {
          cell.border = {
            top: { style: 'thin', color: { argb: 'FF0066CC' } },
            bottom: { style: 'thin', color: { argb: 'FF0066CC' } },
            left: { style: 'thin', color: { argb: 'FF0066CC' } },
            right: { style: 'thin', color: { argb: 'FF0066CC' } },
          };
          cell.alignment = { horizontal: 'center', vertical: 'middle' };
        });

        // Sayısal değerler için locale formatting
        companyRow.getCell(4).numFmt = '#,##0';
        companyRow.getCell(5).numFmt = '0.0';

        currentRow += 2; // Boş satır bırak
      });

    // Column genişlikleri
    worksheet.getColumn(1).width = 25; // Temsilci
    worksheet.getColumn(2).width = 15; // Kanal
    worksheet.getColumn(3).width = 20; // Firma
    worksheet.getColumn(4).width = 18; // AI Analiz Adedi
    worksheet.getColumn(5).width = 22; // AI Analiz Ortalama Skoru

    // Header border'ları
    const headerRowObj = worksheet.getRow(3);
    headerRowObj.eachCell((cell) => {
      cell.border = {
        top: { style: 'thin', color: { argb: 'FF4472C4' } },
        bottom: { style: 'thin', color: { argb: 'FF4472C4' } },
        left: { style: 'thin', color: { argb: 'FF4472C4' } },
        right: { style: 'thin', color: { argb: 'FF4472C4' } },
      };
    });

    // Excel dosyasını indir
    const buffer = await workbook.xlsx.writeBuffer();
    const blob = new Blob([buffer], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `Günlük_Temsilci_Analizi_${dateStr.replace(/\//g, '_')}.xlsx`;
    link.click();
    window.URL.revokeObjectURL(url);

    return true;
  } catch (error) {
    console.error('Günlük temsilci analizi raporu oluşturulurken hata:', error);
    throw error;
  }
};
