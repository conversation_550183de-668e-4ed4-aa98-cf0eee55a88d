'use client';
import React, { useEffect } from 'react';

export default function Logout() {
  useEffect(() => {
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');

    setTimeout(() => {
      window.location.replace('/login');
    }, 100);
  }, []);

  return (
    <div style={{ padding: '20px', textAlign: 'center' }}>
      <p>Çıkış yapılıyor...</p>
    </div>
  );
}
